package com.score.callmetest

import android.app.Application
import android.content.Context
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.manager.AdjustManager
import com.score.callmetest.manager.AppLifecycleManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.GooglePlayBillingManager
import com.score.callmetest.manager.InstallReferrerManager
import com.score.callmetest.manager.LogReportManager
import com.score.callmetest.manager.RingtoneManager
import com.score.callmetest.manager.RongCloudCommandManager
import com.score.callmetest.support.crash.CrashHandler
import com.score.callmetest.support.crash.CrashReportingTree
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.ToastUtils
import timber.log.Timber

class CallmeApplication : Application() {

    companion object {
        private lateinit var instance: CallmeApplication

        /**
         * 获取应用程序实例
         */
        fun getInstance(): CallmeApplication {
            return instance
        }

        /**
         * 获取应用程序上下文
         */
        val context: Context
            get() = instance!!.applicationContext
    }

    override fun onCreate() {
        super.onCreate()
        instance = this
        // 初始化日志工具
        val crashReportingTree = CrashReportingTree()
        Timber.plant(
            if (BuildConfig.DEBUG) {
                // Debug模式下同时使用DebugTree和CrashReportingTree
                Timber.DebugTree()
            } else {
                // Release模式下只使用CrashReportingTree
                crashReportingTree
            }
        )

        // 如果是Debug模式，额外添加CrashReportingTree用于日志收集
        if (BuildConfig.DEBUG) {
            Timber.plant(crashReportingTree)
        }

        // 初始化崩溃处理器
        CrashHandler.init()

        DisplayUtils.init(this)
        ToastUtils.init(this)

        // 初始化融云SDK
        // todo-dsc 暂时每次都需要登录，登录后再init
//        RongCloudManager.init(this)

        AppLifecycleManager.init(this)

        // Google Play 相关初始化和补单逻辑全部收拢到 GooglePlayBillingManager
        GooglePlayBillingManager.init(this)

        // 初始化 Adjust
        AdjustManager.init(this)
        InstallReferrerManager.init(this)
        
        // 初始化日志上报管理器
        LogReportManager.resetLogCounter()
    }

    override fun onTerminate() {
        GlobalManager.onAppExit()
        super.onTerminate()

        // 清理融云CommandMessage管理器资源
        RongCloudCommandManager.instance.destroy()

        // 清理融云SDK资源
        RongCloudManager.cleanup()

        // 清理铃声管理器资源
        RingtoneManager.release()

        Timber.tag("RongCloud").d("应用程序终止，资源清理完成")
    }
}
