package com.score.callmetest.support.crash

import android.os.Build
import android.util.Log
import com.score.callmetest.BuildConfig
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * 崩溃和日志收集库
 *
 * 职责：
 * - 统一的日志处理入口
 * - 本地日志文件保存
 * - 预留服务器上报接口（暂不实现）
 */
object FakeCrashLibrary {
    private const val TAG = "FakeCrashLibrary"

    // 协程作用域，用于异步处理
    private val logScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // 是否启用远程上报（预留接口，暂不实现）
    private var isRemoteReportingEnabled = false

    /**
     * 记录警告级别的日志
     * @param message 日志消息
     * @param throwable 异常对象（可选）
     * @param tag 日志标签
     */
    fun logWarning(message: String?, throwable: Throwable?, tag: String?) {
        logScope.launch {
            try {
                // 保存到本地日志文件
                DumpLogHelper.dumpLogToFile(
                    log = buildLogContent("WARNING", message, throwable, tag),
                    tag = tag,
                    priority = android.util.Log.WARN
                )

                // 预留：上报到服务器
                if (isRemoteReportingEnabled) {
                    val logContent = buildLogContent("WARNING", message, throwable, tag)
                    reportLogToServer("warning", logContent)
                }
            } catch (e: Exception) {
                System.err.println("处理警告日志时发生错误: ${e.message}")
            }
        }
    }

    /**
     * 记录错误级别的日志
     * @param message 日志消息
     * @param throwable 异常对象（可选）
     * @param tag 日志标签
     */
    fun logError(message: String?, throwable: Throwable?, tag: String?) {
        logScope.launch {
            try {
                // 保存为崩溃日志（支持只有消息或只有异常的情况）
                DumpLogHelper.dumpCrashToFile(
                    ex = throwable,
                    message = message,
                    tag = tag
                )

                // 预留：上报到服务器
                if (isRemoteReportingEnabled) {
                    val logContent = buildLogContent("ERROR", message, throwable, tag)
                    reportLogToServer("error", logContent)
                }
            } catch (e: Exception) {
                System.err.println("处理错误日志时发生错误: ${e.message}")
            }
        }
    }

    /**
     * 构建日志内容
     */
    private fun buildLogContent(level: String, message: String?, throwable: Throwable?, tag: String?): String {
        return buildString {
            append("[$level]")
            if (tag != null) {
                append(" [$tag]")
            }
            append(" ${System.currentTimeMillis()}")
            append("\nMessage: ${message ?: "No message"}")

            if (throwable != null) {
                append("\nException: ${throwable.javaClass.simpleName}")
                append("\nException Message: ${throwable.message}")
                append("\nStack Trace:\n${Log.getStackTraceString(throwable)}")
            }
            append("\n" + "=".repeat(50))
        }
    }

    /**
     * 上报日志到服务器（预留接口）
     * @param logType 日志类型
     * @param logContent 日志内容
     */
    private suspend fun reportLogToServer(logType: String, logContent: String) {
        // TODO: 实现服务器上报逻辑-如果需要的话
        // 可以集成现有的 LogReportManager 或其他上报服务
    }

    /**
     * 设置远程上报开关
     */
    fun setRemoteReportingEnabled(enabled: Boolean) {
        isRemoteReportingEnabled = enabled
    }

    /**
     * 获取远程上报状态
     */
    fun isRemoteReportingEnabled(): Boolean {
        return isRemoteReportingEnabled
    }
}