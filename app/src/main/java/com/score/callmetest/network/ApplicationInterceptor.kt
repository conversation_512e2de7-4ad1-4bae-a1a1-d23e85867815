package com.score.callmetest.network

import com.score.callmetest.CallmeApplication
import com.score.callmetest.util.NetUtils
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

/**
 *  应用层的拦截
 */
class ApplicationInterceptor: Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        if(!NetUtils.isConnected(CallmeApplication.context)){
            throw IOException("no net...")
        }else {
            return chain.proceed(chain.request())
        }
    }
}