package com.score.callmetest.network

import retrofit2.Retrofit
import okhttp3.OkHttpClient
import java.util.concurrent.TimeUnit
import android.os.Build
import com.score.callmetest.BuildConfig
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.Constant
import com.score.callmetest.CallmeApplication
import android.util.Log
import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import com.score.callmetest.util.DeviceUtils
import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.logging.HttpLoggingInterceptor
import okhttp3.Interceptor
import okio.Buffer
import org.json.JSONObject
import okhttp3.Request
import com.score.callmetest.util.AESUtils
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.LanguageUtils
import com.score.callmetest.util.TimeZoneUtils
import com.score.callmetest.util.logAsTag
import okhttp3.Headers
import okhttp3.Protocol
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import com.score.callmetest.util.HeaderUtils
import retrofit2.converter.gson.GsonConverterFactory

object RetrofitUtils {

    // 生成32位密钥
    private fun generateKey(): String {
        val baseKey = Constant.getBaseUrl()
        return if (baseKey.length >= 32) {
            baseKey.substring(0, 32)
        } else {
            baseKey + "0".repeat(32 - baseKey.length)
        }
    }

    // 根据请求URL获取对应的加密密钥
    private fun getEncryptKey(request: Request): String {
        val urlPath = request.url.encodedPath
        return if (urlPath.endsWith("/config/getAppConfigPostV2") || urlPath.endsWith("baseApi/resource/network_top_hash/patch")) {
            // getAppConfigPostV2接口使用generateKey()
            generateKey()
        } else {
            // 其他接口优先从DecryptedAppConfig获取encrypt_key，没有再从SharedPreference获取
            val configEncryptKey = AppConfigManager.getEncryptKey()
            if (!configEncryptKey.isNullOrEmpty()) {
                configEncryptKey
            } else {
                SharePreferenceUtil.getString(Constant.ENCRYPT_KEY, "") ?: generateKey()
            }
        }
    }

    private val okHttpClient: OkHttpClient by lazy {
        val logging = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true) // 启用自动重试
            .addInterceptor { chain ->
                val context = CallmeApplication.context
                val original = chain.request()
                val builder = original.newBuilder()

                // 使用HeaderUtils统一构建header
                val headers = com.score.callmetest.util.HeaderUtils.buildCommonHeaders(context)
                for ((key, value) in headers) {
                    builder.header(key, value)
                }

                val request = builder.build()
                if (request.method == "POST") {
                    val newRequest = addHeadersToBody(request)
                    chain.proceed(newRequest)
                } else {
                    chain.proceed(request)
                }
            }
            .addInterceptor(ApplicationInterceptor())
            .addInterceptor(CurlLoggingInterceptor())
            .addInterceptor(DecryptResponseInterceptor())
//            .addInterceptor(logging)
            .addInterceptor { chain ->
                try {
                    chain.proceed(chain.request())
                } catch (e: java.net.SocketTimeoutException) {
                    // 这里可以全局处理超时异常，比如弹Toast
                    Log.e("NetworkTimeout", "请求超时: ${e.localizedMessage}")
                    throw e
                }
            }
            .build()
    }

    private fun addHeadersToBody(request: Request): Request {
        val originalBody = request.body

        // 将headers转换为字符串
        val headersMap = mutableMapOf<String, String>()
        request.headers.forEach { (name, value) ->
            headersMap[name] = value.toString()
        }
        val headersString = JSONObject(headersMap).toString().replace("\\/", "/")

        // 准备要加密的JSON字符串
        val jsonToEncrypt = if (originalBody == null) {
            // 如果原始请求体为空，创建新的JSON对象
            JSONObject().apply {
                put("http_headers", headersString)
            }.toString().replace("\\/", "/")
        } else {
            // 读取原始请求体
            val buffer = Buffer()
            originalBody.writeTo(buffer)
            val contentType = originalBody.contentType()
            val charset = contentType?.charset(Charsets.UTF_8) ?: Charsets.UTF_8
            val bodyString = buffer.readString(charset)

            if (bodyString.isNullOrEmpty()) {
                // 如果原始请求体为空，创建新的JSON对象
                JSONObject().apply {
                    put("http_headers", headersString)
                }.toString().replace("\\/", "/")
            } else {
                try {
                    // 解析原始请求体
                    val bodyJson = JSONObject(bodyString)

                    // 添加 headers
                    bodyJson.put(
                        "http_headers",
                        JSONObject(headersString).toString().replace("\\/", "/")
                    )
                    bodyJson.toString().replace("\\/", "/")
                } catch (e: Exception) {
                    // 如果原始请求体不是JSON格式，返回原始请求
                    return request
                }
            }
        }

        // 使用AES加密
        try {
            val encryptKey = getEncryptKey(request)
            ("${request.url}, 加密key: " + encryptKey + ", 加密前：" + jsonToEncrypt).logAsTag(
                javaClass.name
            )

            val encryptedData = AESUtils.encrypt(jsonToEncrypt, encryptKey)

//            ("${request.url}, 加密key: " + encryptKey + ", 加密后：" + encryptedData).logAsTag(javaClass.name)

            // 直接将加密后的数据作为原始请求体，并移除所有headers
            val newBody = encryptedData.toRequestBody("application/json".toMediaType())

            return request.newBuilder()
                .method(request.method, newBody)
                .headers(Headers.Builder().build()) // 清空所有headers
                .header("content-type", "application/json")
                .build()
        } catch (e: Exception) {
            // 如果加密失败，返回原始请求
            return request
        }
    }

    val retrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl("https://" + Constant.getBaseUrl())
            .client(okHttpClient)
//            .addConverterFactory(json.asConverterFactory("application/json".toMediaType()))
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    val apiService: ApiService by lazy {
        retrofit.create(ApiService::class.java)
    }
    
    // 日志上报专用的Retrofit实例，使用BASE_LOG_URL
    val logRetrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl("https://" + Constant.getLogUrl())
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    val logApiService: ApiService by lazy {
        logRetrofit.create(ApiService::class.java)
    }

    // Curl 日志拦截器
    class CurlLoggingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val curlCmd = StringBuilder()
            curlCmd.append("curl -X ").append(request.method)
            curlCmd.append(" '").append(request.url).append("'")
            for (name in request.headers.names()) {
                val value = request.header(name)
                curlCmd.append(" -H '").append(name).append(": ").append(value).append("'")
            }
            val requestBody = request.body
            if (requestBody != null) {
                val buffer = Buffer()
                requestBody.writeTo(buffer)
                val charset = requestBody.contentType()?.charset(Charsets.UTF_8) ?: Charsets.UTF_8
                val body = buffer.readString(charset)
                if (body.isNotEmpty()) {
                    curlCmd.append(" --data '").append(body.replace("'", "\\'")).append("'")
                }
            }
//            println("[CURL] $curlCmd")
            "[CURL] $curlCmd".logAsTag(javaClass.name, Log.WARN)
            return chain.proceed(request)
        }
    }

    // 响应体解密拦截器
    class DecryptResponseInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            try {
                var response = chain.proceed(request)

                val responseBody = response.body ?: return response
                val contentType = responseBody.contentType()
                val encryptedString = responseBody.string().replace("\r\n", "")

                response = try {
                    val decryptKey = getEncryptKey(request)
//                    ("${request.url}, 解密key: " + decryptKey + ", 解密前：" + encryptedString).logAsTag(
//                        javaClass.name
//                    )
                    val decryptedString = AESUtils.decrypt(encryptedString, decryptKey)
                    ("${request.url}, 解密key: " + decryptKey + ", 解密后：" + decryptedString).logAsTag(
                        javaClass.name
                    )

                    val newBody = decryptedString.toResponseBody(contentType)
                    response.newBuilder().body(newBody).build()
                } catch (e: Exception) {
                    Log.e(this.javaClass.name, "解密出错： ", e)
                    // 解密失败，返回原始 response
                    response
                }
                return response
            } catch (e: Exception) {
                // 捕获超时等异常，返回一个408的空response，避免崩溃
                return Response.Builder()
                    .request(request)
                    .protocol(Protocol.HTTP_1_1)
                    .code(408) // 408 Request Timeout
                    .message("timeout: ${e.javaClass.simpleName}")
                    .body("".toResponseBody(null))
                    .build()
            }
        }
    }
} 