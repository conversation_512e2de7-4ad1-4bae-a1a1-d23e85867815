package com.score.callmetest.network

import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import android.util.Base64
import com.google.gson.annotations.SerializedName
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.util.AESUtils

@Serializable
data class TokenBody(val token: String)


@Serializable
data class ConfigPostV2(
    val ver: Int = 0
)


@Serializable
data class GetBroadcasterExtraInfoRequest(
    val userId: String? = null
)


@Serializable
data class GetGiftCountRequest(
    val searchType: Int = 15,    ////查询的礼物类型,1:查询普通礼物、4:查询特效礼物、8:查询活动礼物、5:查询普通+特效礼物、9:查询普通+活动礼物、12:查询特效+活动礼物、15:查询全部，(2、3、6，7、11、13、14预留，现1与3等效，现4与6等效，现5与7等效，现8与10等效，现9与11等效,现12与14等效,现13与15等效)
    val userId: String? = null
)


// 创建通话频道接口请求参数
@Serializable
data class CreateChannelRequest(
    val bigoUid: String? = null, // bigo uid
    val callCardType: Int? = null, // 通话卡类型，10:一分钟免费通话卡
    val callMode: String? = null, // 通话模式
    val callSource: Int? = null, // 拨打来源
    val callType: Int? = null, // 通话类型(1-私人电话 2-随机匹配)
    val clientSessionId: String? = null, // 客户端会话id
    val isFree: Boolean? = false, // 是否免费通话
    val isMatchRealRobot: Boolean = false, // 是否匹配真人机器人
    val isQuickMatch: Boolean = false, // 是否快速匹配
    val matchGender: Int? = null, // 匹配性别
    val playSupportType: String? = "1", // 播放支持类型（0-所有 1-推流 2-播放器）
    val requestNo: String? = null, // requestNo
    val supportVideoSdks: List<Int>? = null, // 支持视频通话SDK列表
    val toUserId: String? = null // 接收者id
)

/**
 * 接听通话接口请求参数
 * @param channelName 频道名称
 */
@Serializable
data class PickUpRequest(
    val channelName: String? = null // 频道名称
)

/**
 * 查询用户最近通话记录请求参数
 * @param vs 版本号
 */
@Serializable
data class LatelyRecordRequest(
    val vs: Int? = null // 版本号
)

/**
 * 挂断通话接口请求参数
 */
@Serializable
data class HangUpRequest(
    val channelName: String? = null, // 频道名称∂
    val groupName: String? = null,
    val handUpReason: Int? = null, // 挂断原因
    val requestNo: String? = null,
    val remark: String? = null // 备注
)

/**
 * 创建充值订单接口请求参数
 */
@Serializable
data class CreateRechargeRequest(
    val email: String? = null, // 用户邮箱
    val entry: String = RechargeSource.SUBSCRIBE_DETAIL.source, // 充值入口
    val goodsCode: String? = null, // 商品编号
    val payChannel: String? = null, // 支付通道（IAP-苹果内购 GP-谷歌支付）
    val source: String? = null // 充值邀请码，主播邀请链接，活动商品等特殊附带赠送比例的需要传
)

@Serializable
data class SearchBroadcastersRequest(
    val limit: Int = 20,
    val page: Int = 1,
    val isPageMode: Boolean = true,
    val isRemoteImageUrl: Boolean = true,
    val tag: String = "All",
    val category: String = "",
    val region: String? = null
)

@Serializable
data class GetUserListOnlineStatusPostV2Request(
    val userIds: List<String>? = null
)


@Serializable
data class GetUserOnlineStatusPostV2Request(
    val userId: String? = null
)

@Serializable
data class GetUserInfoPostV2Request(
    val userId: String? = null
)

/**
 * GooglePlay订单支付校验接口请求参数
 */
@Serializable
data class GooglePlayPaymentVerifyRequest(
    val orderNo: String? = null, // 支付订单号
    val purchaseData: String? = null, // 支付有效载荷
    val signature: String? = null // 签名
)


/**
 * 查询商品列表接口请求参数
 */
@Serializable
data class QueryGoodsListRequest(
    val isIncludeSubscription: Boolean = false, // 是否包含订阅商品
    val payChannel: String = "GP" // 支付通道（IAP-苹果内购 GP-谷歌支付）
)


/**
 * 搜索主播邀请链接的商品列表请求参数
 */
@Serializable
data class BroadcasterInvitationGoodsRequest(
    val invitationId: String? = null, // 邀请链接id
    val payChannel: String = "GP" // 支付通道（IAP-苹果内购 GP-谷歌支付）
)


/**
 * 查询充值结果接口请求参数
 */
@Serializable
data class RechargeSearchRequest(
    val orderNo: String? = null // 支付订单号
)

/**
 * 搜索主播墙列表请求参数
 * isPageMode说明：
 * 不传或者传false 只根据limit递增返回数据,page传1即可；
 * 传true就是正常的分页模式，page从1开始递增，limit传分页大小，
 * 新包建议传true
 */
@Serializable
data class BroadcasterWallSearchRequest(
    val browserId: String? = null, // 浏览器Id(仅为H5需要传递)
    val category: String? = null, // 分类
    val isRemoteImageUrl: Boolean? = null, // 是否返回远程图片地址
    val limit: Int? = null, // 每页大小(10或者20)
    val page: Int? = null, // 页码
    val tag: String? = null, // 主播标签
    val isPageMode: Boolean? = true // 是否分页模式,默认是false
)

/**
 * 更新Agora uid请求参数
 */
@Serializable
data class UpdateAgoraUidRequest(
    val agoraUid: String? = null // agora用户ID
)


/**
 * 加入频道接口请求参数
 */
@Serializable
data class JoinChannelRequest(
    val channelName: String? = null, // 频道名称
    val channelUid: String? = null, // 第三方返回的频道内用户uid
    val requestNo: String? = null, // requestNo
    val userId: Int? = null // 成功加入频道的用户Id
)

/**
 * 赠送礼物请求参数
 */
@Serializable
data class GiveGiftRequest(
    val channelName: String? = null, // 通话频道
    val giftCode: String? = null, // 礼物编号
    val num: Int? = null, // 赠送数量
    val recipientUserId: String? = null // 受赠用户ID
)

/**
 * 查询通话结果请求参数
 */
@Serializable
data class CallResultRequest(
    val channelName: String? = null, // 通话频道名称
    val isRemoteImageUrl: Boolean? = true // 是否返回远程图片地址
)

/**
 * 查询用户头像请求参数
 * @param avatar 头像相对路径
 * @param isHorizontal 是否横版
 * @param userId 用户Id
 */
@Serializable
data class AvatarSearchRequest(
    val avatar: String? = null, // 头像相对路径
    val isHorizontal: Boolean? = null, // 是否横版
    val userId: Int? = null // 用户Id
)


/**
 * 评价主播请求参数
 * @param channelName 通话频道
 * @param score 评分(好评是1，差评是0)
 * @param tags 标签集合
 */
@Serializable
data class BroadcasterEvaluateRequest(
    val channelName: String? = null, // 通话频道
    val score: Int = 1, // 评分(好评是1，差评是0)
    val tags: List<String>? = null // 标签集合
)

/**
 * 获取通话时长请求参数
 * @param channelName 通话频道名称
 */
@Serializable
data class CallDurationRequest(
    val channelName: String? = null // 通话频道名称
)

/**
 * 查询促销商品请求参数
 * @param isIncludeSubscription 是否包含订阅商品
 * @param payChannel 支付通道（IAP-苹果内购 GP-谷歌支付）
 */
@Serializable
data class PromotionGoodsRequest(
    val bizVer: String? = null,   //业务版本，用于控制不同业务逻辑 ,
    val isIncludeSubscription: Boolean = false, // 是否包含订阅商品
    val payChannel: String = "GP" // 支付通道（IAP-苹果内购 GP-谷歌支付）
)

/**
 * 获取推送优惠信息请求参数
 * @param invitationId 邀请链接id
 * @param payChannel 支付通道（IAP-苹果内购 GP-谷歌支付）
 */
@Serializable
data class SpecialOfferRequest(
    val bizVer: String? = null,   //业务版本，用于控制不同业务逻辑 ,
    val invitationId: String? = null, // 邀请链接id
    val payChannel: String? = null // 支付通道（IAP-苹果内购 GP-谷歌支付）
)

/**
 * 添加/删除好友请求参数
 * @param followUserId 关注的好友id（String类型）
 * @param operateType 操作类型 1=关注 0=取消关注
 * @param requestNo 请求流水号
 * @param source 来源 0=默认
 */
@Serializable
data class AddFriendRequest(
    val followUserId: String? = null, // 关注的好友id（String类型）
    val operateType: Int? = null, // 操作类型 1=关注 0=取消关注
    val requestNo: String? = null, // 请求流水号
    val source: Int? = null // 来源 0=默认
)

@Serializable
data class UnFriendRequest(
    val followUserId: String? = null, // 关注的好友id（String类型）
    val requestNo: String? = null, // 请求流水号
)

/**
 * 更新头像请求参数
 * @param avatarPath 头像路径
 */
@Serializable
data class UpdateAvatarRequest(
    val avatarPath: String? = null // 头像路径
)

/**
 * 更新媒体资源请求参数
 * @param actionType 新增=1,更新=2,删除=3
 * @param coins 设置的媒体价格
 * @param deleteMediaId 要删除的mediaId
 * @param mediaPath mediaPath
 * @param mediaType mediaType (图片：photo  视频：video)
 * @param replaceMediaId 之前的MediaId
 */
@Serializable
data class UpdateMediaRequest(
    val actionType: Int? = null, // 新增=1,更新=2,删除=3
    val coins: Int? = null, // 设置的媒体价格
    val deleteMediaId: String? = null, // 要删除的mediaId
    val mediaPath: String? = null, // mediaPath
    val mediaType: String? = null, // mediaType (图片：photo  视频：video)
    val replaceMediaId: String? = null // 之前的MediaId
)

/**
 * 保存用户信息请求参数
 * @param about 个性签名
 * @param birthday 出生日期 (XXXX-XX-XX)
 * @param country 国家
 * @param language 语言
 * @param nickname 昵称
 */
@Serializable
data class SaveUserInfoRequest(
    val about: String? = null, // 个性签名
    val birthday: String? = null, // 出生日期 (XXXX-XX-XX)
    val country: String? = null, // 国家
    val language: String? = null, // 语言
    val nickname: String? = null // 昵称
)

/**
 * 保存用户基本信息请求参数
 * @param birthday 出生日期 (XXXX-XX-XX)
 * @param country 国家
 * @param gender 性别(1:男性 2:女性)
 * @param invitationCode 邀请码（选填）
 * @param nickname 昵称
 */
@Serializable
data class SaveBasicInfoRequest(
    val birthday: String? = null, // 出生日期 (XXXX-XX-XX)
    val country: String? = null, // 国家
    val gender: Int? = null, // 性别(1:男性 2:女性)
    val invitationCode: String? = null, // 邀请码（选填）
    val nickname: String? = null // 昵称
)

/**
 * 主播排行榜列表查询请求参数
 * @param count 排行榜数量
 */
@Serializable
data class BroadcasterRankSearchRequest(
    val count: Int? = null // 排行榜数量
)

/**
 * 用户排行榜列表查询请求参数
 * @param count 排行榜数量
 */
@Serializable
data class UserRankSearchRequest(
    val count: Int? = null // 排行榜数量
)

/**
 * 用户前后台切换上报请求参数、
 */
@Serializable
data class ModeSwitchRequest(
    val mode: Int? = null // 模式（0-前台 1-后台）
)

/**
 * 投诉与屏蔽请求参数
 */
@Serializable
data class ComplainInsertRecordRequest(
    val broadcasterId: String? = null, // 被投诉/屏蔽的主播id
    val channelName: String? = null, // 频道名称
    val complainCategory: String? = null, // 投诉分类
    val complainSub: String? = null, // 投诉子项分类
    val isAudit: Boolean? = false,
    val reason: String? = null, // 投诉原因
    val snapshotPath: String? = null // 截图路径
)

/**
 * 取消屏蔽请求参数
 * @param blockUserId 屏蔽者的id
 */
@Serializable
data class RemoveBlockRequest(
    val blockUserId: String? = null // 屏蔽者的id
)

/**
 * AF归因主动上报请求参数
 * @param adgroupId 广告组ID
 * @param adset 广告组
 * @param adsetId 广告组ID
 * @param afChannel AF渠道
 * @param afStatus AF状态
 * @param agency 代理商
 * @param campaign 活动
 * @param campaignId 活动ID
 * @param createTime 创建时间
 * @param deviceId 设备ID
 * @param id ID
 * @param pkg 包名
 * @param userId 用户ID
 * @param utmSource utm来源
 * @param ver 版本
 */
@Serializable
data class AfAttributionRecordRequest(
    val adgroupId: String? = null, // 广告组ID
    val adset: String? = null, // 广告组
    val adsetId: String? = null, // 广告组ID
    val afChannel: String? = null, // AF渠道
    val afStatus: String? = null, // AF状态
    val agency: String? = null, // 代理商
    val campaign: String? = null, // 活动
    val campaignId: String? = null, // 活动ID
    val createTime: Int? = null, // 创建时间
    val deviceId: String? = null, // 设备ID
    val id: Int? = null, // ID
    val pkg: String? = null, // 包名
    val userId: String? = null, // 用户ID
    val utmSource: String? = null, // utm来源
    val ver: String? = null, // 版本
    // 新增归因SDK及版本、fbInstallReferrer
    val attributionSdk: String? = null, // 当前使用的归因SDK（AJ/AF）
    val attributionSdkVer: String? = null, // 当前归因SDK版本
    val fbInstallReferrer: String? = null // Adjust归因回调的fbInstallReferrer
)

/**
 * IM聊天限制记录请求参数
 * @param broadcasterId 主播Id
 */
@Serializable
data class CreateImSessionRequest(
    val broadcasterId: Int? = null // 主播Id
)

/**
 * 用户绑定邮箱请求参数
 * @param confirmEmail 确认邮箱
 * @param newEmail 新邮箱
 * @param userId 用户ID
 */
@Serializable
data class BindEmailRequest(
    val confirmEmail: String? = null, // 确认邮箱
    val newEmail: String? = null, // 新邮箱
    val userId: Int? = null // 用户ID
)

/**
 * 用户密码确认与修改请求参数
 * @param isUpdate 是否修改密码
 * @param password 密码
 */
@Serializable
data class UserPasswordRequest(
    val isUpdate: Boolean? = null, // 是否修改密码
    val password: String? = null // 密码
)

/**
 * 一键关注打招呼请求参数
 * @param broadcasterIds 主播ids
 * @param userId 用户id
 */
@Serializable
data class SendRecommendedBroadcasterRequest(
    val broadcasterIds: List<String>? = null, // 主播ids
    val userId: String? = null // 用户id
)

/**
 * 通知后台机器人呼叫/IM请求参数
 * @param broadcasterId 主播id
 */
@Serializable
data class RobotActionRequest(
    val broadcasterId: Int? = null // 主播id
)

/**
 * 用户跳转界面上报请求参数
 * @param sign 详情页 UserDetail
 */
@Serializable
data class SkipPageRequest(
    val sign: String? = null // 详情页 UserDetail
)

/**
 * 批量查询图片远程url请求参数
 * @param mediaPath 媒体路径
 * @param mediaType 类型：photo/video
 * @param userId 用户id
 */
@Serializable
data class MediaUrlRequest(
    val mediaPath: String? = null,
    val mediaType: String? = null, // photo/video
    val userId: Int? = null
)

/**
 * 免打扰开关请求参数
 * @param isSwitchNotDisturbCall 是否打开通话免打扰
 * @param isSwitchNotDisturbIm 是否打开IM免打扰
 */
@Serializable
data class NotDisturbSwitchRequest(
    val isSwitchNotDisturbCall: Boolean? = null,
    val isSwitchNotDisturbIm: Boolean? = null
)

/**
 * FlashChat 快速匹配请求参数
 * @param batchId 匹配队列id
 * @param clientSessionId 当次匹配id
 * @param callSource 呼叫来源
 * @param supportVideoSdks 支持的视频SDK列表
 */
@Serializable
data class FlashChatRequest(
    val batchId: String? = null,
    val clientSessionId: String? = null,
    val callSource: Int = 12,
    val supportVideoSdks: List<Int> = listOf(1)
)

/**
 * 订阅商品获取请求参数
 * @param payChannel 支付通道（IAP-苹果内购 GP-谷歌支付）
 */
@Serializable
data class SubscriptionSearchRequest(
    val payChannel: String = "GP" // IAP-苹果内购 GP-谷歌支付
)

@Serializable
data class LoginRequest(
    val oauthType: Int,
    val token: String,
    val relogin: Int? = null,
    val info: String? = null
)

@Serializable
data class RiskInfo(
    val platform: String,
    val pkg: String,
    val ver: String,
    val platform_ver: String,
    val model: String,
    val user_id: String? = null,
    val device_id: String,
    val is_enable_vpn: String? = null,
    val is_enable_proxy: String? = null,
    val system_language: String,
    val sim_country: String? = null,
    val time_zone: String,
    val is_auto_time_zone: String? = null,
    val gps_longitude: String? = null,
    val gps_latitude: String? = null,
    val input_language: String? = null,
    val is_china_ios_device: String? = null
)

/**
 * rc-token
 */
@Serializable
data class RcTokenRequest(
    val appKey: String,
    val bannerByPrivate: Boolean = true
)

/**
 * 用户follow请求
 */
@Serializable
data class UserFollowPageRequest(
    val isSole: Boolean = false,  //为true 时唯一关系;仅为关注或被关注 忽略互关
    val limit: Int = 15,  //每页大小
    val page: Int = 1, //当前页
    val targetUserMinTimestamp: Long = 0,  //目标用户最小活跃时间
    val type: Int = 1,   //关系类型 1-friends;2-follow;3-following
    val useSetFollow: Boolean = false  //粉丝列表/关注列表标记出 ,互关用户 ,与isSole 不可共用
)

/**
 * 屏蔽列表请求参数
 */
@Serializable
data class UserBlockListRequest(
    val limit: Int = 15,  //每页大小
    val page: Int = 1, //当前页
)