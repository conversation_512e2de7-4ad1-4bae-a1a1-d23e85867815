package com.score.callmetest.util

import android.Manifest
import android.annotation.SuppressLint
import android.app.ActivityManager
import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Environment
import android.os.StatFs
import android.provider.Settings
import android.telephony.TelephonyManager
import android.util.Log
import android.view.WindowManager
import androidx.annotation.RequiresPermission
import androidx.core.app.ActivityCompat
import com.score.callmetest.CallmeApplication
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.IOException

/**
 * Android 设备信息工具类，用于获取设备相关信息
 */
object DeviceUtils {
    private const val TAG = "DeviceUtils"

    /**
     * 获取设备唯一标识（Android ID）
     */
    @SuppressLint("HardwareIds")
    @JvmStatic
    fun getAndroidId(): String {
        return try {
            Settings.Secure.getString(CallmeApplication.context.contentResolver, Settings.Secure.ANDROID_ID)
        } catch (e: Exception) {
            Log.e(TAG, "获取Android ID失败", e)
            ""
        }
    }

    /**
     * 获取设备型号（如：Redmi K30）
     */
    @JvmStatic
    fun getDeviceModel(): String {
        return Build.MODEL.trim()
    }

    /**
     * 获取设备厂商（如：Xiaomi）
     */
    @JvmStatic
    fun getDeviceManufacturer(): String {
        return Build.MANUFACTURER.trim()
    }

    /**
     * 获取系统版本（如：11）
     */
    @JvmStatic
    fun getSystemVersion(): String {
        return Build.VERSION.RELEASE
    }

    /**
     * 获取系统API级别
     */
    @JvmStatic
    fun getSystemApiLevel(): Int {
        return Build.VERSION.SDK_INT
    }

    /**
     * 获取应用版本名称
     */
    @JvmStatic
    fun getAppVersionName(context: Context): String {
        return try {
            val packageManager = context.packageManager
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.getPackageInfo(context.packageName, PackageManager.PackageInfoFlags.of(0))
            } else {
                @Suppress("DEPRECATION")
                packageManager.getPackageInfo(context.packageName, 0)
            }
            packageInfo.versionName
        } catch (e: Exception) {
            Log.e(TAG, "获取应用版本名称失败", e)
            "1.0.0"
        }.toString()
    }

    /**
     * 获取应用版本号
     */
    @JvmStatic
    fun getAppVersionCode(context: Context): Long {
        return try {
            val packageManager = context.packageManager
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.getPackageInfo(context.packageName, PackageManager.PackageInfoFlags.of(0))
            } else {
                @Suppress("DEPRECATION")
                packageManager.getPackageInfo(context.packageName, 0)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.longVersionCode
            } else {
                @Suppress("DEPRECATION")
                packageInfo.versionCode.toLong()
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取应用版本号失败", e)
            1L
        }
    }

    /**
     * 获取手机屏幕宽度（像素）
     */
    @JvmStatic
    fun getScreenWidth(context: Context): Int {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val bounds = wm.currentWindowMetrics.bounds
            bounds.width()
        } else {
            val metrics = context.resources.displayMetrics
            metrics.widthPixels
        }
    }

    /**
     * 获取手机屏幕高度（像素）
     */
    @JvmStatic
    fun getScreenHeight(context: Context): Int {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val bounds = wm.currentWindowMetrics.bounds
            bounds.height()
        } else {
            val metrics = context.resources.displayMetrics
            metrics.heightPixels
        }
    }

    /**
     * 获取屏幕密度（如：2.0）
     */
    @JvmStatic
    fun getScreenDensity(context: Context): Float {
        val metrics = context.resources.displayMetrics
        return metrics.density
    }

    /**
     * 获取屏幕密度DPI（如：320）
     */
    @JvmStatic
    fun getScreenDensityDpi(context: Context): Int {
        val metrics = context.resources.displayMetrics
        return metrics.densityDpi
    }

    /**
     * 获取屏幕方向（0:未知 1:竖屏 2:横屏）
     */
    @JvmStatic
    fun getScreenOrientation(context: Context): Int {
        val orientation = context.resources.configuration.orientation
        return when (orientation) {
            Configuration.ORIENTATION_PORTRAIT -> 1
            Configuration.ORIENTATION_LANDSCAPE -> 2
            else -> 0
        }
    }

    /**
     * 获取设备总内存（字节）
     */
    @JvmStatic
    fun getTotalMemory(): Long {
        return try {
            val reader = BufferedReader(FileReader("/proc/meminfo"))
            val line = reader.readLine()
            reader.close()
            val totalMemory = line.substring(line.indexOf(':') + 1).trim()
                .split("\\s+".toRegex()).firstOrNull { it.isNotBlank() }?.toLongOrNull() ?: 0
            totalMemory * 1024 // KB转Byte
        } catch (e: IOException) {
            Log.e(TAG, "获取设备总内存失败", e)
            0
        }
    }

    /**
     * 获取可用内存（字节）
     */
    @JvmStatic
    fun getAvailableMemory(context: Context): Long {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                val memoryInfo = ActivityManager.MemoryInfo()
                activityManager.getMemoryInfo(memoryInfo)
                memoryInfo.availMem
            } else {
                // 对于API级别低于16的设备，使用旧方法
                val reader = BufferedReader(FileReader("/proc/meminfo"))
                var line: String?
                var availableMemory = 0L
                while (reader.readLine().also { line = it } != null) {
                    if (line!!.startsWith("MemFree:")) {
                        availableMemory = line.substring(line.indexOf(':') + 1).trim()
                            .split("\\s+".toRegex()).firstOrNull { it.isNotBlank() }?.toLongOrNull() ?: 0
                        break
                    }
                }
                reader.close()
                availableMemory * 1024 // KB转Byte
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取可用内存失败", e)
            0
        }
    }

    /**
     * 获取内部存储总空间（字节）
     */
    @JvmStatic
    fun getInternalStorageTotal(): Long {
        return try {
            val path = Environment.getDataDirectory()
            val statFs = StatFs(path.path)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                statFs.totalBytes
            } else {
                @Suppress("DEPRECATION")
                (statFs.blockCount.toLong() * statFs.blockSize.toLong())
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取内部存储总空间失败", e)
            0
        }
    }

    /**
     * 获取内部存储可用空间（字节）
     */
    @JvmStatic
    fun getInternalStorageAvailable(): Long {
        return try {
            val path = Environment.getDataDirectory()
            val statFs = StatFs(path.path)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                statFs.availableBytes
            } else {
                @Suppress("DEPRECATION")
                (statFs.availableBlocks.toLong() * statFs.blockSize.toLong())
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取内部存储可用空间失败", e)
            0
        }
    }

    /**
     * 获取外部存储总空间（字节）
     */
    @JvmStatic
    fun getExternalStorageTotal(): Long {
        return if (!isExternalStorageAvailable()) 0 else try {
            val path = Environment.getExternalStorageDirectory()
            val statFs = StatFs(path.path)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                statFs.totalBytes
            } else {
                @Suppress("DEPRECATION")
                (statFs.blockCount.toLong() * statFs.blockSize.toLong())
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取外部存储总空间失败", e)
            0
        }
    }

    /**
     * 获取外部存储可用空间（字节）
     */
    @JvmStatic
    fun getExternalStorageAvailable(): Long {
        return if (!isExternalStorageAvailable()) 0 else try {
            val path = Environment.getExternalStorageDirectory()
            val statFs = StatFs(path.path)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                statFs.availableBytes
            } else {
                @Suppress("DEPRECATION")
                (statFs.availableBlocks.toLong() * statFs.blockSize.toLong())
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取外部存储可用空间失败", e)
            0
        }
    }

    /**
     * 检查外部存储是否可用
     */
    @JvmStatic
    fun isExternalStorageAvailable(): Boolean {
        val state = Environment.getExternalStorageState()
        return state == Environment.MEDIA_MOUNTED
    }

    /**
     * 获取网络连接类型
     * @return 网络类型：0-未知 1-WIFI 2-移动数据 3-无网络
     */
    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    @JvmStatic
    fun getNetworkType(context: Context): Int {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork ?: return 3
                val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return 3

                when {
                    capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> 1
                    capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> 2
                    else -> 0
                }
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo

                if (networkInfo == null || !networkInfo.isConnected) {
                    3 // 无网络
                } else if (networkInfo.type == ConnectivityManager.TYPE_WIFI) {
                    1 // WIFI
                } else if (networkInfo.type == ConnectivityManager.TYPE_MOBILE) {
                    2 // 移动数据
                } else {
                    0 // 未知
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取网络类型失败", e)
            0
        }
    }

    /**
     * 获取网络连接状态描述
     */
    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    @JvmStatic
    fun getNetworkStateDescription(context: Context): String {
        return when (getNetworkType(context)) {
            1 -> "WIFI"
            2 -> "移动数据"
            3 -> "无网络连接"
            else -> "未知网络"
        }
    }

    /**
     * 获取WiFi MAC地址
     */
    @JvmStatic
    @SuppressLint("HardwareIds")
    fun getWifiMacAddress(context: Context): String {
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return "需要位置权限"
        }

        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android Q及以上版本无法获取真实MAC地址
                "Android Q及以上版本限制获取MAC地址"
            } else {
                val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
                val wifiInfo = wifiManager.connectionInfo
                wifiInfo.macAddress ?: "未知"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取WiFi MAC地址失败", e)
            "未知"
        }
    }

    /**
     * 获取设备IMEI（需要READ_PHONE_STATE权限）
     */
    @RequiresPermission("android.permission.READ_PRIVILEGED_PHONE_STATE")
    @JvmStatic
    @SuppressLint("HardwareIds")
    fun getIMEI(context: Context): String {
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_PHONE_STATE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return "需要读取手机状态权限"
        }

        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                telephonyManager.imei ?: "未知"
            } else {
                @Suppress("DEPRECATION")
                telephonyManager.deviceId ?: "未知"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取IMEI失败", e)
            "未知"
        }
    }

    /**
     * 获取设备IMSI（需要READ_PHONE_STATE权限）
     */
    @RequiresPermission("android.permission.READ_PRIVILEGED_PHONE_STATE")
    @JvmStatic
    @SuppressLint("HardwareIds")
    fun getIMSI(context: Context): String {
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_PHONE_STATE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return "需要读取手机状态权限"
        }

        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            @Suppress("DEPRECATION")
            telephonyManager.subscriberId ?: "未知"
        } catch (e: Exception) {
            Log.e(TAG, "获取IMSI失败", e)
            "未知"
        }
    }

    /**
     * 获取手机号（需要READ_PHONE_STATE权限）
     */
    @RequiresPermission(anyOf = [Manifest.permission.READ_SMS, Manifest.permission.READ_PHONE_NUMBERS, Manifest.permission.READ_PHONE_STATE])
    @JvmStatic
    @SuppressLint("HardwareIds")
    fun getPhoneNumber(context: Context): String {
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_PHONE_STATE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return "需要读取手机状态权限"
        }

        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            @Suppress("DEPRECATION")
            telephonyManager.line1Number ?: "未知"
        } catch (e: Exception) {
            Log.e(TAG, "获取手机号失败", e)
            "未知"
        }
    }

    /**
     * 获取CPU型号
     */
    @JvmStatic
    fun getCpuModel(): String {
        return try {
            val reader = BufferedReader(FileReader("/proc/cpuinfo"))
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                if (line!!.startsWith("Hardware")) {
                    reader.close()
                    return line.substring(line.indexOf(':') + 1).trim()
                }
            }
            reader.close()
            "未知"
        } catch (e: IOException) {
            Log.e(TAG, "获取CPU型号失败", e)
            "未知"
        }
    }

    /**
     * 获取CPU核心数
     */
    @JvmStatic
    fun getCpuCores(): Int {
        return try {
            // 方法1：通过文件系统获取
            val dir = File("/sys/devices/system/cpu/")
            val files = dir.listFiles() ?: return 1

            var count = 0
            for (file in files) {
                if (file.name.matches(Regex("cpu[0-9]+"))) {
                    count++
                }
            }

            if (count > 0) return count

            // 方法2：通过Runtime获取
            Runtime.getRuntime().availableProcessors()
        } catch (e: Exception) {
            Log.e(TAG, "获取CPU核心数失败", e)
            1
        }
    }

    /**
     * 判断是否是模拟器
     */
    @JvmStatic
    fun isEmulator(): Boolean {
        return (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
                || Build.FINGERPRINT.startsWith("generic")
                || Build.FINGERPRINT.startsWith("unknown")
                || Build.HARDWARE.contains("goldfish")
                || Build.HARDWARE.contains("ranchu")
                || Build.MODEL.contains("google_sdk")
                || Build.MODEL.contains("Emulator")
                || Build.MODEL.contains("Android SDK built for x86")
                || Build.MANUFACTURER.contains("Genymotion")
                || System.getProperty("ro.kernel.qemu") == "1"
    }

    /**
     * 格式化文件大小（字节转可读字符串）
     */
    @JvmStatic
    fun formatFileSize(size: Long): String {
        if (size <= 0) return "0 B"

        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        val digitGroups = (Math.log10(size.toDouble()) / Math.log10(1024.0)).toInt()

        return String.format("%.2f %s", size / Math.pow(1024.0, digitGroups.toDouble()), units[digitGroups])
    }

    /**
     * 获取设备信息摘要
     */
    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    @JvmStatic
    fun getDeviceInfoSummary(context: Context): String {
        return """
            设备型号: ${getDeviceModel()}
            设备厂商: ${getDeviceManufacturer()}
            系统版本: ${getSystemVersion()} (API ${getSystemApiLevel()})
            应用版本: ${getAppVersionName(context)} (${getAppVersionCode(context)})
            屏幕尺寸: ${getScreenWidth(context)}x${getScreenHeight(context)} px
            屏幕密度: ${getScreenDensity(context)} (${getScreenDensityDpi(context)} dpi)
            总内存: ${formatFileSize(getTotalMemory())}
            可用内存: ${formatFileSize(getAvailableMemory(context))}
            内部存储: ${formatFileSize(getInternalStorageAvailable())} / ${formatFileSize(getInternalStorageTotal())}
            外部存储: ${if (isExternalStorageAvailable()) formatFileSize(getExternalStorageAvailable()) else "不可用"} / ${if (isExternalStorageAvailable()) formatFileSize(getExternalStorageTotal()) else "不可用"}
            网络状态: ${getNetworkStateDescription(context)}
            CPU: ${getCpuModel()} (${getCpuCores()}核)
            模拟器: ${if (isEmulator()) "是" else "否"}
        """.trimIndent()
    }
}