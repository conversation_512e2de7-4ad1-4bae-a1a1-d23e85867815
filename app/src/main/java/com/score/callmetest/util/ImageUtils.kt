package com.score.callmetest.util

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.media.ExifInterface
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.util.Size
import androidx.activity.result.ActivityResultLauncher
import com.score.callmetest.ui.preview.MultiImagePreviewActivity
import com.yalantis.ucrop.UCrop
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import kotlin.math.roundToInt

/**
 * Utility class for common image operations
 */
object ImageUtils {
    
    /**
     * Compress an image from Uri with specified quality and max size
     * @param context Context
     * @param imageUri Source image Uri
     * @param quality Compression quality (0-100)
     * @param maxWidth Maximum width of the output image
     * @param maxHeight Maximum height of the output image
     * @param format Output format (default JPEG)
     * @return Path to the compressed image file, or null if compression failed
     */
    fun compressImage(
        context: Context,
        imageUri: Uri,
        quality: Int = 80,
        maxWidth: Int = 1920,
        maxHeight: Int = 1080,
        format: Bitmap.CompressFormat = Bitmap.CompressFormat.JPEG
    ): String? {
        var bitmap: Bitmap? = null
        try {
            // 1. 读取原始尺寸
            val options = BitmapFactory.Options().apply { inJustDecodeBounds = true }
            context.contentResolver.openInputStream(imageUri)?.use {
                BitmapFactory.decodeStream(it, null, options)
            }

            // 2. 采样加载
            val sampleSize = calculateSampleSize(options.outWidth, options.outHeight, maxWidth, maxHeight)
            val decodeOptions = BitmapFactory.Options().apply { inSampleSize = sampleSize }
            context.contentResolver.openInputStream(imageUri)?.use {
                bitmap = BitmapFactory.decodeStream(it, null, decodeOptions)
            }
            if (bitmap == null) return null

            // 3. 精确缩放
            if (bitmap.width > maxWidth || bitmap.height > maxHeight) {
                bitmap = resizeBitmap(bitmap, maxWidth, maxHeight)
            }

            // 4. 输出文件
            val extension = when (format) {
                Bitmap.CompressFormat.JPEG -> ".jpg"
                Bitmap.CompressFormat.PNG -> ".png"
                else -> ".jpg"
            }
            val outFile = File(context.cacheDir, "compressed_${System.currentTimeMillis()}$extension")
            FileOutputStream(outFile).use { outputStream ->
                bitmap!!.compress(format, quality, outputStream)
                outputStream.flush()
            }
            return outFile.absolutePath
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        } finally {
            bitmap?.recycle()
        }
    }

    /**
     * Convert bitmap to byte array
     */
    fun bitmapToByteArray(bitmap: Bitmap, format: Bitmap.CompressFormat = Bitmap.CompressFormat.JPEG, quality: Int = 100): ByteArray {
        val stream = ByteArrayOutputStream()
        bitmap.compress(format, quality, stream)
        return stream.toByteArray()
    }

    /**
     * Resize bitmap maintaining aspect ratio
     */
    fun resizeBitmap(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val ratioBitmap = width.toFloat() / height.toFloat()
        val ratioMax = maxWidth.toFloat() / maxHeight.toFloat()

        var finalWidth = maxWidth
        var finalHeight = maxHeight
        if (ratioMax > ratioBitmap) {
            finalWidth = (maxHeight.toFloat() * ratioBitmap).roundToInt()
        } else {
            finalHeight = (maxWidth.toFloat() / ratioBitmap).roundToInt()
        }

        return Bitmap.createScaledBitmap(bitmap, finalWidth, finalHeight, true)
    }

    /**
     * Get image dimensions without loading the full image into memory
     */
    fun getImageDimensions(context: Context, uri: Uri): Size? {
        try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                BitmapFactory.decodeStream(inputStream, null, options)
            }
            return if (options.outWidth > 0 && options.outHeight > 0) {
                Size(options.outWidth, options.outHeight)
            } else {
                null
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    /**
     * Calculate optimal sample size for loading large bitmaps efficiently
     */
    private fun calculateSampleSize(width: Int, height: Int, reqWidth: Int, reqHeight: Int): Int {
        var inSampleSize = 1
        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2
            while (halfHeight / inSampleSize >= reqHeight && halfWidth / inSampleSize >= reqWidth) {
                inSampleSize *= 2
            }
        }
        return inSampleSize
    }

    /**
     * Rotate image according to EXIF orientation
     */
    fun rotateImageIfNeeded(context: Context, uri: Uri, bitmap: Bitmap): Bitmap {
        try {
            val inputStream = context.contentResolver.openInputStream(uri) ?: return bitmap
            val exif = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                ExifInterface(inputStream)
            } else {
                val path = uri.path
                if (path != null) ExifInterface(path) else return bitmap
            }

            val orientation = exif.getAttributeInt(
                ExifInterface.TAG_ORIENTATION,
                ExifInterface.ORIENTATION_NORMAL
            )

            val matrix = Matrix()
            when (orientation) {
                ExifInterface.ORIENTATION_ROTATE_90 -> matrix.postRotate(90f)
                ExifInterface.ORIENTATION_ROTATE_180 -> matrix.postRotate(180f)
                ExifInterface.ORIENTATION_ROTATE_270 -> matrix.postRotate(270f)
                else -> return bitmap
            }

            return try {
                Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
            } catch (e: OutOfMemoryError) {
                e.printStackTrace()
                bitmap
            }
        } catch (e: IOException) {
            e.printStackTrace()
            return bitmap
        }
    }

    /**
     * Calculate file size in MB
     */
    fun getFileSizeInMB(file: File): Double {
        return file.length().toDouble() / (1024 * 1024)
    }

    /**
     * Check if file is an image based on its MIME type
     */
    fun isImageFile(context: Context, uri: Uri): Boolean {
        val mimeType = context.contentResolver.getType(uri)
        return mimeType?.startsWith("image/") == true
    }

    /**
     * 启动 uCrop 裁剪图片
     * @param activity 当前Activity
     * @param sourceUri 源图片Uri
     * @param destFileName 输出文件名
     * @param aspectX 裁剪宽高比X
     * @param aspectY 裁剪宽高比Y
     * @param maxSize 最大输出尺寸
     * @param launcher ActivityResultLauncher<Intent>
     */
    fun startUCrop(
        activity: android.app.Activity,
        sourceUri: Uri,
        destFileName: String = "cropped_${System.currentTimeMillis()}.jpg",
        aspectX: Float = 1f,
        aspectY: Float = 1f,
        maxSize: Int = 600,
        launcher: ActivityResultLauncher<Intent>
    ) {
        val destUri = Uri.fromFile(File(activity.cacheDir, destFileName))
        val uCrop = UCrop.of(sourceUri, destUri)
            .withAspectRatio(aspectX, aspectY)
            .withMaxResultSize(maxSize, maxSize)
        val intent = uCrop.getIntent(activity)
        launcher.launch(intent)
    }

    /**
     * 最佳实践：图片预览与裁剪（优先系统裁剪，兜底uCrop）
     * @param activity 当前Activity
     * @param sourceUri 源图片Uri
     * @param cropLauncher 裁剪ActivityResultLauncher
     */
    fun previewAndCropImage(
        activity: android.app.Activity,
        sourceUri: Uri,
        cropLauncher: ActivityResultLauncher<Intent>
    ) {
        // 直接进入裁剪页面
        startUCrop(activity, sourceUri, launcher = cropLauncher)
    }

    /**
     * 判断系统是否支持裁剪
     */
    fun isSystemCropAvailable(activity: android.app.Activity): Boolean {
        val intent = Intent("com.android.camera.action.CROP")
        val list = activity.packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
        return list.isNotEmpty()
    }

    /**
     * 启动系统裁剪
     */
    fun startSystemCrop(
        activity: android.app.Activity,
        sourceUri: Uri,
        launcher: ActivityResultLauncher<Intent>,
        destFileName: String = "avatar_system_crop_${System.currentTimeMillis()}.jpg"
    ) {
        val destUri = Uri.fromFile(File(activity.cacheDir, destFileName))
        val intent = Intent("com.android.camera.action.CROP").apply {
            setDataAndType(sourceUri, "image/*")
            putExtra("crop", "true")
            putExtra("aspectX", 1)
            putExtra("aspectY", 1)
            putExtra("outputX", 600)
            putExtra("outputY", 600)
            putExtra("scale", true)
            putExtra("return-data", false)
            putExtra(MediaStore.EXTRA_OUTPUT, destUri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        launcher.launch(intent)
    }

    /**
     * 启动多图大图预览
     * @param activity 当前Activity
     * @param imageUris 图片Uri列表
     * @param startIndex 初始显示索引
     */
    fun previewImages(
        activity: android.app.Activity,
        imageUris: List<Uri>,
        startIndex: Int = 0
    ) {
        val intent = Intent(activity, MultiImagePreviewActivity::class.java)
        intent.putParcelableArrayListExtra("imageUris", ArrayList(imageUris))
        intent.putExtra("startIndex", startIndex)
        activity.startActivity(intent)
    }
} 