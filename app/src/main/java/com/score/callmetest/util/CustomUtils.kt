package com.score.callmetest.util

import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 当前项目抽离出公用的一些功能
 */
object CustomUtils {

    /**
     * 格式化时间戳
     *
     * <1天（显示hh:mm）
     * 大于1天（显示MM-dd hh:mm）
     * 非今年消息（yyyy-MM-DD hh:mm）
     */
    fun formatTimestampForChatList(timestamp: Long): String {
        val calendar = Calendar.getInstance()
        val currentYear = calendar.get(Calendar.YEAR)
        val currentDay = calendar.get(Calendar.DAY_OF_YEAR)

        val date = Date(timestamp)
        val messageCalendar = Calendar.getInstance().apply {
            time = date
        }
        val messageYear = messageCalendar.get(Calendar.YEAR)
        val messageDay = messageCalendar.get(Calendar.DAY_OF_YEAR)

        return when {
            // 今天内的消息，显示时分
            currentYear == messageYear && currentDay == messageDay -> {
                SimpleDateFormat("HH:mm", Locale.getDefault()).format(date)
            }
            // 非今年的消息，显示年月日时分
            currentYear != messageYear -> {
                SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(date)
            }
            // 今年内但不是今天的消息，显示月日时分
            else -> {
                SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()).format(date)
            }
        }
    }


    /**
     *  播放一次svga
     *
     * @param [svgaView] view
     * @param [assetName] svga-name
     */
    fun playSvgaOnce(svgaView: SVGAImageView, assetName: String?) {
        playSvga(svgaView,assetName,1)
    }

    /**
     *  播放svga
     *
     * @param [svgaView] view
     * @param [assetName] svga-name
     * @param [loops] 播放次数--默认无限
     */
    fun playSvga(svgaView: SVGAImageView, assetName: String?,loops: Int = 0) {
        if (assetName == null) return
        SVGAParser.shareParser().apply {
            init(CallmeApplication.context)
            decodeFromAssets(assetName,object : SVGAParser.ParseCompletion {
                override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                    svgaView.setImageDrawable(SVGADrawable(videoItem))
                    svgaView.loops = loops
                    svgaView.startAnimation()
                }

                override fun onError() {}
            })
        }
    }

    /**
     * 根据礼物名称获取对应的资源ID
     *
     * @param giftName 礼物名称
     * @return 资源ID
     */
    fun getGiftResIdByName(giftName: String): Int {
        // 这里实现根据礼物名称查找资源ID的逻辑
        return when (giftName) {
            "angel_staff" -> R.drawable.angel_staff
            "aimashibao" -> R.drawable.aimashibao
            "bear" -> R.drawable.bear
            "biao" -> R.drawable.biao
            "bieshu" -> R.drawable.bieshu
            "blue_rose" -> R.drawable.blue_rose
            "cake" -> R.drawable.cake
            "castle" -> R.drawable.castle
            "champagne" -> R.drawable.champagne
            "chuang" -> R.drawable.chuang
            "chengbao" -> R.drawable.chengbao
            "chocolate" -> R.drawable.chocolate
            "chungao" -> R.drawable.chungao
            "crystal_bottle" -> R.drawable.crystal_bottle
            "crystal_key" -> R.drawable.crystal_key
            "crystal_shoes" -> R.drawable.crystal_shoes
            "cupid" -> R.drawable.cupid
            "diamond_goose" -> R.drawable.diamond_goose
            "erhuan" -> R.drawable.erhuan
            "feiji" -> R.drawable.feiji
            "fries" -> R.drawable.fries
            "great" -> R.drawable.great
            "heart" -> R.drawable.heart
            "hot_air_ballon" -> R.drawable.hot_air_ballon
            "huangguan" -> R.drawable.huangguan
            "hunsha" -> R.drawable.hunsha
            "jiezi" -> R.drawable.jiezi
            "kiss" -> R.drawable.kiss
            "lipstick" -> R.drawable.lipstick
            "lollipop" -> R.drawable.lollipop
            "luxury_bag" -> R.drawable.luxury_bag
            "meigui" -> R.drawable.meigui
            "paoche" -> R.drawable.paoche
            "perfume" -> R.drawable.perfume
            "qinggua" -> R.drawable.qinggua
            "qiaokeli" -> R.drawable.qiaokeli
            "rich_gun" -> R.drawable.rich_gun
            "ring" -> R.drawable.ring
            "rocket" -> R.drawable.rocket
            "rose" -> R.drawable.rose
            "shouzhuo" -> R.drawable.shouzhuo
            "shuijingxie" -> R.drawable.shuijingxie
            "supercar" -> R.drawable.supercar
            "xiangbing" -> R.drawable.xiangbin
            "xiangbin" -> R.drawable.xiangbin
            "xiangjiao" -> R.drawable.xiangjiao
            "xianglian" -> R.drawable.xianglian
            "xiangshui" -> R.drawable.xiangshui
            "xiong" -> R.drawable.xiong
            "yongyi" -> R.drawable.yongyi
            "youlun" -> R.drawable.youlun
            else -> R.drawable.gift_placehold
        }
    }
}