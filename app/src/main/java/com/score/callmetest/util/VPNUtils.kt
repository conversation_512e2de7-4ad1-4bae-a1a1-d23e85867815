package com.score.callmetest.util

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import com.score.callmetest.CallmeApplication

object VPNUtils {
    
    /**
     * 检测是否启用VPN
     * @return true: 启用VPN, false: 未启用VPN
     */
    fun isVPNEnabled(): <PERSON><PERSON><PERSON> {
        return try {
            val connectivityManager = CallmeApplication.context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val network = connectivityManager.activeNetwork
            if (network != null) {
                val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_VPN) ?: false
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
} 