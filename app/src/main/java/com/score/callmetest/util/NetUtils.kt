package com.score.callmetest.util

import android.annotation.SuppressLint
import android.content.Context
import android.telephony.TelephonyManager

import android.net.ConnectivityManager
import android.net.Proxy
import android.text.TextUtils
import timber.log.Timber

import android.net.NetworkCapabilities

import android.net.Network

import android.net.LinkProperties

import android.net.ConnectivityManager.NetworkCallback

import android.net.NetworkRequest
import android.os.Build
import androidx.annotation.RequiresApi

object NetUtils {

    /**
     * 没有网络
     */
    const val NETWORK_TYPE_INVALID = 0

    /**
     * wap网络
     */
    const val NETWORK_TYPE_WAP = 1

    /**
     * 2G网络
     */
    const val NETWORK_TYPE_2G = 2

    /**
     * 3G和3G以上网络，或统称为快速网络
     */
    const val NETWORK_TYPE_3G = 3

    /**
     * wifi网络
     */
    const val NETWORK_TYPE_WIFI = 4

    /**
     * 判断网络是否连接
     */
    fun isConnected(context: Context): Boolean {
        val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val ni = cm.activeNetworkInfo
        return ni != null && ni.isConnectedOrConnecting && ni.isAvailable
    }

    /**
     * 判断是否是wifi连接
     */
    fun isWifi(context: Context): Boolean {
        return getNetWorkType(context) === NETWORK_TYPE_WIFI
    }

    /**
     * 获取网络状态，wifi,wap,2g,3g.
     *
     * @param context 上下文
     * @return int 网络状态 [.NETWORK_TYPE_2G],[.NETWORK_TYPE_3G],
     * [.NETWORK_TYPE_INVALID],[.NETWORK_TYPE_WAP],[.NETWORK_TYPE_WIFI]
     */
    fun getNetWorkType(context: Context): Int {
        var netWorkType = -1
        val manager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkInfo = manager.activeNetworkInfo
        if (networkInfo != null && networkInfo.isConnected) {
            val type = networkInfo.typeName
            if (type.equals("WIFI", ignoreCase = true)) {
                netWorkType = NETWORK_TYPE_WIFI
            } else if (type.equals("MOBILE", ignoreCase = true)) {
                val proxyHost = Proxy.getDefaultHost()
                netWorkType =
                    if (TextUtils.isEmpty(proxyHost)) if (isFastMobileNetwork(context)) NETWORK_TYPE_3G else NETWORK_TYPE_2G else NETWORK_TYPE_WAP
            }
        } else {
            netWorkType = NETWORK_TYPE_INVALID
        }
        return netWorkType
    }

    /**
     * 监听网络变化（7.0以上不能静态注册android.net.conn.CONNECTIVITY_CHANGE，
     * 故用此方案解决，除此还可以动态注册）
     * @param context
     */
    fun registerNetLinstener(context: Context) {
        val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        cm.requestNetwork(NetworkRequest.Builder().build(), object : NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                Timber.d("onAvailable--网络可用")
            }

            override fun onUnavailable() {
                super.onUnavailable()
                // 不触发。。。
                Timber.d("onUnavailable--网络不可用")
            }

            override fun onLost(network: Network) {
                super.onLost(network)
                Timber.d("onLost--网络失去连接")

            }

            override fun onLinkPropertiesChanged(network: Network, linkProperties: LinkProperties) {
                super.onLinkPropertiesChanged(network, linkProperties)
                // 网络属性更改 例：设置代理、移动转wifi
                Timber.d("onLinkPropertiesChanged--网络属性更改")
            }

            override fun onLosing(network: Network, maxMsToLive: Int) {
                super.onLosing(network, maxMsToLive)
                // 不回调这个。。。
                Timber.d("onLosing--正在失去连接？？")
            }

            override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
                super.onCapabilitiesChanged(network, networkCapabilities)
                // 时不时在调用，有网的时候
                Timber.d("onCapabilitiesChanged--。。网络")
            }
        })
    }

    /**
     * 需要READ_PHONE_STATE权限
     */
    @SuppressLint("MissingPermission")
    private fun isFastMobileNetwork(context: Context): Boolean {
        val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        return when (telephonyManager.networkType) {
            TelephonyManager.NETWORK_TYPE_1xRTT -> false // ~ 50-100 kbps
            TelephonyManager.NETWORK_TYPE_CDMA -> false // ~ 14-64 kbps
            TelephonyManager.NETWORK_TYPE_EDGE -> false // ~ 50-100 kbps
            TelephonyManager.NETWORK_TYPE_EVDO_0 -> true // ~ 400-1000 kbps
            TelephonyManager.NETWORK_TYPE_EVDO_A -> true // ~ 600-1400 kbps
            TelephonyManager.NETWORK_TYPE_GPRS -> false // ~ 100 kbps
            TelephonyManager.NETWORK_TYPE_HSDPA -> true // ~ 2-14 Mbps
            TelephonyManager.NETWORK_TYPE_HSPA -> true // ~ 700-1700 kbps
            TelephonyManager.NETWORK_TYPE_HSUPA -> true // ~ 1-23 Mbps
            TelephonyManager.NETWORK_TYPE_UMTS -> true // ~ 400-7000 kbps
            TelephonyManager.NETWORK_TYPE_EHRPD -> true // ~ 1-2 Mbps
            TelephonyManager.NETWORK_TYPE_EVDO_B -> true // ~ 5 Mbps
            TelephonyManager.NETWORK_TYPE_HSPAP -> true // ~ 10-20 Mbps
            TelephonyManager.NETWORK_TYPE_IDEN -> false // ~25 kbps
            TelephonyManager.NETWORK_TYPE_LTE -> true // ~ 10+ Mbps
            TelephonyManager.NETWORK_TYPE_UNKNOWN -> false
            else -> false
        }
    }

}