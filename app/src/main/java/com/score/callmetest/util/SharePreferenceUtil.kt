package com.score.callmetest.util

import android.content.Context
import android.content.SharedPreferences
import com.score.callmetest.CallmeApplication
import androidx.core.content.edit

object SharePreferenceUtil {
    private fun getPrefs(name: String = "default_prefs"): SharedPreferences {
        return CallmeApplication.Companion.context.getSharedPreferences(name, Context.MODE_PRIVATE)
    }

    fun putString(key: String, value: String, name: String = "default_prefs") {
        getPrefs(name).edit { putString(key, value) }
    }

    fun getString(key: String, defValue: String? = null, name: String = "default_prefs"): String? {
        return getPrefs(name).getString(key, defValue)
    }

    fun putBoolean(key: String, value: Boolean, name: String = "default_prefs") {
        getPrefs(name).edit { putBoolean(key, value) }
    }

    fun getBoolean(key: String, defValue: Boolean = false, name: String = "default_prefs"): Boolean {
        return getPrefs(name).getBoolean(key, defValue)
    }

    fun putInt(key: String, value: Int, name: String = "default_prefs") {
        getPrefs(name).edit { putInt(key, value) }
    }

    fun getInt(key: String, defValue: Int = 0, name: String = "default_prefs"): Int {
        return getPrefs(name).getInt(key, defValue)
    }

    fun putLong(key: String, value: Long, name: String = "default_prefs") {
        getPrefs(name).edit { putLong(key, value) }
    }

    fun getLong(key: String, defValue: Long = 0L, name: String = "default_prefs"): Long {
        return getPrefs(name).getLong(key, defValue)
    }

    fun putFloat(key: String, value: Float, name: String = "default_prefs") {
        getPrefs(name).edit { putFloat(key, value) }
    }

    fun getFloat(key: String, defValue: Float = 0f, name: String = "default_prefs"): Float {
        return getPrefs(name).getFloat(key, defValue)
    }

    fun remove(key: String, name: String = "default_prefs") {
        getPrefs(name).edit { remove(key) }
    }

    fun clear(name: String = "default_prefs") {
        getPrefs(name).edit { clear() }
    }
} 