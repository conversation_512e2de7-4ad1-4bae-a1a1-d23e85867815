package com.score.callmetest.util

import java.util.concurrent.TimeUnit

object TimeUtils {


    /**
     * 工具方法：将秒数转换为 hh:mm:ss 格式的时间字符串
     *
     * @param seconds 输入的秒数
     * @return 格式化后的时间字符串，格式为 hh:mm:ss
     */
     fun formatSecondsToTime(seconds: Long): String {
        val h = seconds / 3600
        val m = (seconds % 3600) / 60
        val s = seconds % 60
        return String.format("%02d:%02d:%02d", h, m, s)
    }

    /**
     * 将毫秒数格式化为 hh:mm:ss 格式的时间字符串
     *
     * @param milliseconds 输入的毫秒数
     * @return 格式化后的时间字符串，格式为 hh:mm:ss
     */
    fun formatTime(milliseconds: Long): String {
        val hours = TimeUnit.MILLISECONDS.toHours(milliseconds)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(milliseconds) % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(milliseconds) % 60
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }
}