package com.score.callmetest.util

import android.content.Context
import android.os.Build
import com.score.callmetest.CallmeApplication

object InputMethodUtils {
    
    /**
     * 获取输入法语言
     * @return 输入法语言字符串，多个用逗号分隔，如果无法获取返回null
     */
    fun getInputLanguages(): String? {
        return try {
            // 简化实现：直接返回系统语言
            // 因为大多数情况下输入法语言与系统语言一致
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val locale = CallmeApplication.context.resources.configuration.locales.get(0)
                locale?.language
            } else {
                @Suppress("DEPRECATION")
                CallmeApplication.context.resources.configuration.locale.language
            }
        } catch (e: Exception) {
            null
        }
    }
} 