package com.score.callmetest.util

import android.app.Activity
import android.content.Context
import android.os.Build
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import androidx.annotation.ColorInt
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsControllerCompat

object StatusBarUtils {
    /**
     * 兼容 minSdkVersion 21，显示状态栏并自动设置图标颜色（API 23+ 支持深色图标）
     * 仅非常浅的颜色（如白色、浅灰）才会用深色图标，红色、蓝色等高饱和度色彩一律用白色图标，保证可读性和兼容性。
     * @param activity Activity
     * @param color 状态栏颜色
     * @param darkIcons 状态栏图标是否为深色（true: 深色图标，适用于浅色背景；false: 浅色图标，适用于深色背景）。为 null 时自动判断。
     */
    @Suppress("DEPRECATION")
    fun showStatusBar(activity: Activity, @ColorInt color: Int, darkIcons: Boolean? = null) {
        val window = activity.window
        window.statusBarColor = color
        val decorView = window.decorView ?: return
        
        // 自动判断图标颜色，只有非常浅的颜色才用深色图标
        val useDarkIcons = darkIcons ?: isLightColor(color)
        
        // 清除 FULLSCREEN 标志，显示状态栏
        var flags = decorView.systemUiVisibility
        flags = flags and View.SYSTEM_UI_FLAG_FULLSCREEN.inv()
        
        // 只有 API 23+ 才能设置状态栏图标颜色
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags = if (useDarkIcons) {
                flags or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
            } else {
                flags and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
            }
        }
        decorView.systemUiVisibility = flags
        
        // 特殊处理Xiaomi等厂商系统
        handleVendorSpecificStatusBar(activity, useDarkIcons)
    }

    /**
     * 处理厂商特定的状态栏设置
     */
    private fun handleVendorSpecificStatusBar(activity: Activity, darkIcons: Boolean) {
        val manufacturer = Build.MANUFACTURER.lowercase()
        
        when {
            manufacturer.contains("xiaomi") || manufacturer.contains("redmi") -> {
                // Xiaomi系统特殊处理
                try {
                    val window = activity.window
                    val layoutParams = window.attributes
                    
                    // 设置状态栏透明
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        window.statusBarColor = android.graphics.Color.TRANSPARENT
                    }
                    
                    // 设置布局标志
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        WindowCompat.setDecorFitsSystemWindows(window, false)
                    } else {
                        @Suppress("DEPRECATION")
                        window.decorView.systemUiVisibility = window.decorView.systemUiVisibility or
                                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    }
                    
                    // 应用布局参数
                    window.attributes = layoutParams
                } catch (e: Exception) {
                    // 忽略异常，使用默认处理
                }
            }
            
            manufacturer.contains("huawei") || manufacturer.contains("honor") -> {
                // Huawei系统特殊处理
                try {
                    val window = activity.window
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        WindowCompat.setDecorFitsSystemWindows(window, false)
                    }
                } catch (e: Exception) {
                    // 忽略异常，使用默认处理
                }
            }
            
            manufacturer.contains("oppo") || manufacturer.contains("oneplus") -> {
                // OPPO/OnePlus系统特殊处理
                try {
                    val window = activity.window
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        WindowCompat.setDecorFitsSystemWindows(window, false)
                    }
                } catch (e: Exception) {
                    // 忽略异常，使用默认处理
                }
            }
            
            manufacturer.contains("vivo") -> {
                // vivo系统特殊处理
                try {
                    val window = activity.window
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        WindowCompat.setDecorFitsSystemWindows(window, false)
                    }
                } catch (e: Exception) {
                    // 忽略异常，使用默认处理
                }
            }
        }
    }

    /**
     * 判断颜色是否为浅色（适合用深色图标）
     * 阈值设为0.7，只有非常浅的颜色才返回true，红色、蓝色等高饱和度色彩一律用白色图标。
     */
    private fun isLightColor(@ColorInt color: Int): Boolean {
        val r = (color shr 16) and 0xff
        val g = (color shr 8) and 0xff
        val b = color and 0xff
        // 计算相对亮度（sRGB Luminance）
        val luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
        return luminance > 0.7
    }

    /**
     * 隐藏状态栏
     * @param activity Activity
     */
    fun hideStatusBar(activity: Activity) {
        val window = activity.window ?: return
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // window.insetsController 可能为 null，需用 decorView 获取
            val decorView = window.decorView
            val insetsController = decorView?.windowInsetsController
            insetsController?.hide(WindowInsets.Type.statusBars())
        } else {
            @Suppress("DEPRECATION")
            val decorView = window.decorView
            decorView.systemUiVisibility =
                decorView.systemUiVisibility or View.SYSTEM_UI_FLAG_FULLSCREEN
        }
    }

    // 获取状态栏高度
    fun getStatusBarHeight(activity: Activity): Int {
        val resourceId = activity.resources.getIdentifier("status_bar_height", "dimen", "android")
        return if (resourceId > 0) activity.resources.getDimensionPixelSize(resourceId) else 0
    }

    // 获取导航栏高度
    fun getNavigationBarHeight(context: Context): Int {
        val resourceId = context.resources.getIdentifier("navigation_bar_height", "dimen", "android")
        return if (resourceId > 0) context.resources.getDimensionPixelSize(resourceId) else 0
    }

    // 判断状态栏是否可见
    fun isStatusBarVisible(activity: Activity): Boolean {
        val window = activity.window
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val insets = window.decorView.rootWindowInsets
            insets?.isVisible(WindowInsets.Type.statusBars()) ?: true
        } else {
            @Suppress("DEPRECATION")
            val flags = window.decorView.systemUiVisibility
            (flags and View.SYSTEM_UI_FLAG_FULLSCREEN) == 0
        }
    }

    /**
     * 设置状态栏透明并启用沉浸式模式
     * @param activity Activity
     * @param darkIcons 是否使用深色图标
     */
    fun setStatusBarTransparent(activity: Activity, darkIcons: Boolean = false) {
        val window = activity.window
        
        // 设置状态栏透明
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.statusBarColor = android.graphics.Color.TRANSPARENT
        }
        
        // 设置系统UI标志
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            WindowCompat.setDecorFitsSystemWindows(window, false)
        } else {
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility =
                window.decorView.systemUiVisibility or 
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or 
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        }
        
        // 设置状态栏图标颜色
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            @Suppress("DEPRECATION")
            val flags = window.decorView.systemUiVisibility
            window.decorView.systemUiVisibility = if (darkIcons) {
                flags or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
            } else {
                flags and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
            }
        }
        
        // 特殊处理厂商系统
        handleVendorSpecificStatusBar(activity, darkIcons)
    }

    /**
     * 设置全屏沉浸模式
     */
    fun setFullscreenImmersive(activity: Activity) {
        val window = activity.window
        window.decorView.systemUiVisibility =
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                    View.SYSTEM_UI_FLAG_FULLSCREEN or
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        window.statusBarColor = android.graphics.Color.TRANSPARENT
    }
} 