package com.score.callmetest.ui.mine.blockList

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.score.callmetest.databinding.FragmentFollowContentBinding
import com.score.callmetest.ui.base.BaseFragment
import android.view.View
import com.score.callmetest.util.ToastUtils

class BlockListFragment : BaseFragment<FragmentFollowContentBinding, BlockListViewModel>() {

    private lateinit var adapter: BlockListAdapter
    private var mNeedScrollToTop = false

    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentFollowContentBinding {
        return FragmentFollowContentBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = BlockListViewModel::class.java

    override fun initView() {
        adapter = BlockListAdapter { item ->
            item.broadcasterId?.let {
                viewModel.unblock(it) { success ->
                    if (success) {
                        ToastUtils.showShortToast("Unblock successfully")
                        viewModel.fetchBlockList()
                    } else {
                        ToastUtils.showShortToast("Unblock failed")
                    }
                }
            }
        }
        binding.recyclerView.layoutManager = LinearLayoutManager(context)
        binding.recyclerView.adapter = adapter
        setupSwipeRefresh()
        binding.swipeRefreshLayout.isRefreshing = true
        viewModel.fetchBlockList(limit = 15, page = 1)
    }

    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            setOnRefreshListener {
                isRefreshing = true
                mNeedScrollToTop = true
                // 重新加载 block 列表
                viewModel.fetchBlockList(limit = 15, page = 1)
            }
        }
    }

    override fun initData() {
        // 监听 blockList 数据
        viewModel.blockList.observe(viewLifecycleOwner) { list ->
            adapter.setData(list ?: emptyList())
            // 停止刷新动画
            binding.swipeRefreshLayout.isRefreshing = false
            // 空视图显示
            binding.emptyView.visibility = if (list.isNullOrEmpty()) View.VISIBLE else View.GONE
            binding.recyclerView.visibility = if (list.isNullOrEmpty()) View.GONE else View.VISIBLE
            // 下拉刷新时滚动到顶部
            if (mNeedScrollToTop) {
                binding.recyclerView.scrollToPosition(0)
                mNeedScrollToTop = false
            }
        }
        // 监听错误信息
        viewModel.error.observe(viewLifecycleOwner) { msg ->
            if (!msg.isNullOrEmpty()) {
                ToastUtils.showShortToast(msg)
                // 错误发生时，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false
            }
            binding.emptyView.visibility = if (viewModel.blockList.value.isNullOrEmpty()) View.VISIBLE else View.GONE
        }
    }

    override fun initListener() {
        // 可根据需要添加监听
    }
}