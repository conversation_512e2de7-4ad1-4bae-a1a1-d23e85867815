package com.score.callmetest.ui.home

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.network.LastSpecialOfferResponse
import com.score.callmetest.network.PresentedCoinsResponse
import com.score.callmetest.network.PromotionGoodsRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.SpecialOfferRequest
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.launch

class HomeViewModel : ViewModel() {

    // 从 broadcasterWallTagList 获取标签数据
    val broadcasterWallTabDataLists = StrategyManager.getBroadcasterWallTagList()
    
    // 当前选中的国家代码，null表示"All"
    private val _selectedCountryCode = MutableLiveData<String?>("ALL")
    val selectedCountryCode: MutableLiveData<String?> = _selectedCountryCode
    
    fun setSelectedCountry(countryCode: String?) {
        _selectedCountryCode.value = countryCode
    }

    fun getSelectedCountryCode(): String? {
        return _selectedCountryCode.value
    }

    /**
     * 活动促销信息 LiveData
     */
    val promotionModelLiveData = MutableLiveData<LastSpecialOfferResponse?>()
    val promotionLoading = MutableLiveData<Boolean>()

    /**
     * 获取活动促销信息（从接口获取），结果通过 promotionModelLiveData 传递给 UI 层
     * @param request PromotionGoodsRequest（可选，默认 payChannel="GP"）
     */
    suspend fun getPromotionList(request: SpecialOfferRequest = SpecialOfferRequest(bizVer = null, invitationId = null, payChannel = "GP")) {
        promotionLoading.postValue(true)
        try {
            val response = RetrofitUtils.apiService.getLastSpecialOfferV2(request)
            if (response.success) {
                response.data.toString().logAsTag(this.javaClass.name + " getPromotionList: ")
                if (!response.data.isNullOrEmpty()) {
                    promotionModelLiveData.postValue(response.data[0])
                } else {
                    promotionModelLiveData.postValue(null)
                }
            } else {
                promotionModelLiveData.postValue(null)
                ToastUtils.showToast(response.msg ?: "获取促销信息失败")
            }
        } catch (e: Exception) {
            promotionModelLiveData.postValue(null)
            Log.e(this.javaClass.name, "getPromotionList exception", e)
        } finally {
            promotionLoading.postValue(false)
        }
    }

    /**
     * 获取活动促销信息（供UI层调用，自动处理协程和LiveData）
     */
    fun fetchPromotion(request: SpecialOfferRequest = SpecialOfferRequest(bizVer = null, invitationId = null, payChannel = "GP")) {
        promotionLoading.value = true
        viewModelScope.launch {
            try {
                getPromotionList(request)
            } finally {
                promotionLoading.value = false
            }
        }
    }

    val presentedCoinsLiveData = MutableLiveData<GoodsInfo?>()
    val presentedCoinsLoading = MutableLiveData<Boolean>()

    /**
     * 获取新人促销宝箱（供UI层调用，自动处理协程和LiveData）
     */
    fun fetchPresentedCoins(request: PromotionGoodsRequest = PromotionGoodsRequest()) {
        presentedCoinsLoading.value = true
        viewModelScope.launch {
            try {
                val response = RetrofitUtils.apiService.getPromotionGoods(request)
                if (response.success) {
                    presentedCoinsLiveData.value = response.data
                    response.data.toString().logAsTag(this.javaClass.name + " fetchPresentedCoins: ")

                    // 如果没有新人促销数据，自动获取活动促销数据
                    if (response.data == null) {
                        fetchPromotion()
                    }
                } else {
                    response.msg.toString().logAsTag(this.javaClass.name + " fetchPresentedCoins error:")
                    presentedCoinsLiveData.value = null
                    // 获取失败时也尝试获取活动促销数据
                    fetchPromotion()
                }
            } catch (e: Exception) {
                presentedCoinsLiveData.value = null
                // 异常时也尝试获取活动促销数据
                fetchPromotion()
            } finally {
                presentedCoinsLoading.value = false
            }
        }
    }


    data class WallScrollEvent(val state: Int)
}