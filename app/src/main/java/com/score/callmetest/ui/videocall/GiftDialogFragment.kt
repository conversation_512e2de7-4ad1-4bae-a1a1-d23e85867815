package com.score.callmetest.ui.videocall

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.R
import com.score.callmetest.manager.GiftManager
import com.score.callmetest.network.GiftInfo
import com.score.callmetest.ui.main.GiftListAdapter
import kotlinx.coroutines.launch
import androidx.lifecycle.lifecycleScope
import android.graphics.Color
import com.google.android.material.bottomsheet.BottomSheetDialog
import android.app.Dialog
import android.graphics.drawable.GradientDrawable
import android.widget.TextView
import androidx.core.graphics.toColorInt
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.widget.CoinRechargeDialog
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click

class GiftDialogFragment : BottomSheetDialogFragment() {
    private var gifts: List<GiftInfo> = emptyList()
    private var adapter: GiftListAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.dialog_gift, container, false)
        val rvGift = view.findViewById<RecyclerView>(R.id.rv_gift)
        val coinNum = view.findViewById<TextView>(R.id.coin_num)
        val btnRecharge = view.findViewById<TextView>(R.id.btn_recharge)
        val maskView = view.findViewById<View>(R.id.mask_view)
        val coinLayout = view.findViewById<View>(R.id.coin_layout)

        coinNum.text = UserInfoManager.myUserInfo?.availableCoins.toString()
        maskView.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00000000".toColorInt(), "#00000000".toColorInt(), Color.BLACK, Color.BLACK),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM
        )
        GlobalManager.setViewRoundBackground(btnRecharge, "#2ECF40".toColorInt())

        adapter = GiftListAdapter(gifts)
        rvGift.layoutManager = GridLayoutManager(context, 4)
        rvGift.adapter = adapter

        // 加载礼物数据
        viewLifecycleOwner.lifecycleScope.launch {
            gifts = GiftManager.getGiftList()
            adapter?.let {
                it.data = gifts
                it.notifyDataSetChanged()
            }
        }

        adapter?.setOnGiftSentClick { giftInfo ->
            // 点击礼物发送
            // 先验资
            val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
            if (availableCoins < (giftInfo.coinPrice?.toInt() ?: 0)) {
                // 余额不足---去充值
                ToastUtils.showShortToast(getString(R.string.coins_not_enough))
                CoinRechargeDialog().show(childFragmentManager, "coin_recharge")
                return@setOnGiftSentClick
            }
            // 余额足够
            EventBus.post(GiftSendEvent(giftInfo))
        }

        btnRecharge.click {
            CoinRechargeDialog().show(childFragmentManager, "coin_recharge")
        }
        coinLayout.click {
            CoinRechargeDialog().show(childFragmentManager, "coin_recharge")
        }

        // 监听金币余额
        DualChannelEventManager.observeAvailableCoins(this) { availableCoinsMessage ->
            // 更新余额
            val availableCoins = availableCoinsMessage.coins?:0
            UserInfoManager.myUserInfo?.availableCoins = availableCoins
            coinNum.text = availableCoins.toString()
        }

        return view
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener {
            val bottomSheet = (dialog as? BottomSheetDialog)?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundResource(android.R.color.transparent)
        }
        return dialog
    }
}

// EventBus事件
class GiftSendEvent(val gift: GiftInfo)
class GiftRechargeEvent 