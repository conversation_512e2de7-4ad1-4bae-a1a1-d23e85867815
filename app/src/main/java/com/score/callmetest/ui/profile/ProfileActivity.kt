package com.score.callmetest.ui.profile

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.graphics.toColorInt
import androidx.core.widget.TextViewCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityProfileBinding
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.widget.DatePickerBottomSheet
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.ImageUtils
import com.score.callmetest.util.LoadingUtils
import com.score.callmetest.util.SimpleTextWatcher
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import com.yalantis.ucrop.UCrop
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import android.graphics.drawable.GradientDrawable
import android.view.inputmethod.InputMethodManager
import androidx.core.view.doOnPreDraw
import com.score.callmetest.manager.GlobalManager

class ProfileActivity : BaseActivity<ActivityProfileBinding, ProfileViewModel>() {

    private lateinit var photoAdapter: PhotoAdapter
    private val photoList = mutableListOf<Uri>()
    private var selectedImageUri: Uri? = null
    private lateinit var imagePickerLauncher: ActivityResultLauncher<Intent>
    private lateinit var cropImageLauncher: ActivityResultLauncher<Intent>
    private lateinit var photoPickerLauncher: ActivityResultLauncher<Intent>
    private var onImageCroppedCallback: ((Uri) -> Unit)? = null
    
    // 只做展示
    private var age: Int? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        cropImageLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                UCrop.getOutput(result.data!!)?.let { uri ->
                    onImageCroppedCallback?.invoke(uri)
                }
            }
        }
        imagePickerLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                val imageUri = result.data?.data
                if (imageUri != null) {
                    ImageUtils.previewAndCropImage(
                        activity = this,
                        sourceUri = imageUri,
                        cropLauncher = cropImageLauncher
                    )
                }
            }
        }
        photoPickerLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                val imageUri = result.data?.data
                if (imageUri != null && photoList.size < 4) {
                    photoList.add(imageUri)
                    photoAdapter.notifyDataSetChanged()
                    updateAddPhotoButtonVisibility()
                    validateSubmitButton()
                }
            }
        }

    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun getViewBinding(): ActivityProfileBinding {
        return ActivityProfileBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = ProfileViewModel::class.java

    override fun initView() {
        setupToolbar()
        setupProfileData()
        setupPhotoRecyclerView()
        viewModel.recordOriginalProfile(UserInfoManager.myUserInfo)
        // 设置btnDone背景
        DrawableUtils.setShadowBackground(
            binding.btnDone,
            Color.WHITE,
            DisplayUtils.dp2pxInternal(15f).toFloat(),
            ("#52cce5fd").toColorInt(),
            1f // shadowRadius
        )

        // 让输入框支持内部滑动
        binding.etSelfIntroduction.setMovementMethod(android.text.method.ScrollingMovementMethod.getInstance())
        binding.etSelfIntroduction.setOnTouchListener { v, event ->
            v.parent.requestDisallowInterceptTouchEvent(true)
            false
        }
    }

    override fun initListener() {
        binding.btnDone.click {
            if (!validateAllFields() || !viewModel.isProfileChanged()) return@click
            uploadAndSaveAll()
        }

        binding.ivBack.click {
            finish()
        }
        // 头像选择
        binding.avatarLayout.click {
            launchImagePickerAndCrop { uri ->
                selectedImageUri = uri
                GlideUtils.load(this, uri, binding.ivAvatar)
                ToastUtils.showToast("Avatar updated")
                validateSubmitButton()
            }
        }

        binding.btnCopy.click {
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            clipboard.setPrimaryClip(ClipData.newPlainText("User ID", binding.tvId.text))
            ToastUtils.showToast("ID copied to clipboard")
        }
        binding.etNickname.addTextChangedListener(SimpleTextWatcher {
            validateSubmitButton()
            updateCharCount(it,max = 20)
        })
        binding.etSelfIntroduction.addTextChangedListener(SimpleTextWatcher {
            updateCharCount(it)
            validateSubmitButton()
        })

        binding.genderLayout.click { showGenderSelectionDialog() }
        binding.ageLayout.click { showDateOfBirthDialog() }
        binding.regionLayout.click { showCountrySelectionDialog() }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
    }

    private fun setupProfileData() {
        val userInfo = UserInfoManager.myUserInfo
        GlideUtils.load(this, userInfo?.avatarThumbUrl, binding.ivAvatar)

        binding.apply {
            tvId.text = userInfo?.userId.toString()
            etNickname.setText(userInfo?.nickname)

            tvGender.text = if (userInfo?.gender == 1) "Male" else "Female"
            // 只显示年龄
            age = getAgeFromBirthday(userInfo?.birthday).toIntOrNull()
            tvAge.text = age?.toString() ?: "23"
            tvRegion.text = userInfo?.country ?: "India"
            //imageRegion.setImageResource(CountryUtils.getIconByEnName(userInfo?.country))
            etSelfIntroduction.setText(userInfo?.about ?: "")
            updateCharCount(etSelfIntroduction.text.toString())
        }
    }

    private fun setupPhotoRecyclerView() {
        // 初始化photoList为mediaList的thumbUrl或mediaUrl
        photoList.clear()
        UserInfoManager.myUserInfo?.mediaList?.forEach { media ->
            val url = media.mediaUrl
            if (!url.isNullOrEmpty()) {
                photoList.add(Uri.parse(url))
            }
        }

        photoAdapter = PhotoAdapter(
            photoList = photoList,
            onDeleteClick = { position ->
                photoList.removeAt(position)
                photoAdapter.notifyDataSetChanged()
                updateAddPhotoButtonVisibility()
                validateSubmitButton()
            },
            onAddClick = {
                if (photoList.size < 4) {
                    launchImagePickerForPhoto()
                }
            }
        )
        // 直接进入多图大图预览
        photoAdapter.setOnPhotoClickListener { _, position ->
            ImageUtils.previewImages(
                activity = this,
                imageUris = photoList,
                startIndex = position
            )
        }
        binding.photoRecyclerview.apply {
            layoutManager =
                LinearLayoutManager(this@ProfileActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = photoAdapter
        }
    }

    private fun updateAddPhotoButtonVisibility() {
        photoAdapter.updateAddButtonVisibility(photoList.size < 4)
    }

    private fun launchImagePickerAndCrop(onImageCropped: (Uri) -> Unit) {
        AppPermissionManager.checkAndRequestStoragePermission(
            this,
            onGranted = {
                onImageCroppedCallback = onImageCropped
                val intent = Intent(Intent.ACTION_PICK).apply { type = "image/*" }
                imagePickerLauncher.launch(intent)
            },
            onDenied = {
                AppPermissionManager.incrementPermissionCheckCount()
                if (AppPermissionManager.shouldShowPermissionGuide(this)) {
                    showPermissionGuideDialog()
                } else {
                    ToastUtils.showToast("Album permission is required to select avatar")
                }
            }
        )
    }

    private fun launchImagePickerForPhoto() {
        AppPermissionManager.checkAndRequestStoragePermission(
            this,
            onGranted = {
                val intent = Intent(Intent.ACTION_PICK).apply { type = "image/*" }
                photoPickerLauncher.launch(intent)
            },
            onDenied = {
                AppPermissionManager.incrementPermissionCheckCount()
                if (AppPermissionManager.shouldShowPermissionGuide(this)) {
                    showPermissionGuideDialog()
                } else {
                    ToastUtils.showToast("Album permission is required to select photos")
                }
            }
        )
    }
    //根据生日求年龄，默认23
    private fun getAgeFromBirthday(birthday: String?): String {
        if (birthday.isNullOrEmpty()) return "23"
        return try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val birthDate = dateFormat.parse(birthday)
            if (birthDate != null) {
                calculateAge(birthDate).toString()
            } else {
                "23"
            }
        } catch (e: Exception) {
            "23"
        }
    }
    /**
     * 通过生日计算年龄
     */
    private fun calculateAge(birthDate: Date): Int {
        val today = Calendar.getInstance()
        val birthCalendar = Calendar.getInstance().apply { time = birthDate }
        var age = today.get(Calendar.YEAR) - birthCalendar.get(Calendar.YEAR)
        if (today.get(Calendar.DAY_OF_YEAR) < birthCalendar.get(Calendar.DAY_OF_YEAR)) {
            age--
        }
        return age
    }

    /**
     * 展示选择性别弹窗
     */
    private fun showGenderSelectionDialog() {
        val genders = arrayOf("Female", "Male")
        val currentGender = binding.tvGender.text.toString()
        val currentIndex = if (currentGender == "Male") 1 else 0

        val dialog = AlertDialog.Builder(this)
            .setTitle("Select Gender")
            .setSingleChoiceItems(genders, currentIndex) { dialog, which ->
                val selectedGender = genders[which]

                binding.tvGender.text = selectedGender
                validateSubmitButton()
                dialog.dismiss()
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
            }
            .create()

        dialog.show()
    }

    /**
     * 展示显示生日弹窗（Age选项）
     * 目前显示的age是通过birthday算出来的。
     */
    private fun showDateOfBirthDialog() {
        val calendar = Calendar.getInstance()
        val currentYear = calendar.get(Calendar.YEAR)
        val maxYear = currentYear - 18

        val birthday = UserInfoManager.myUserInfo?.birthday

        val datePickerBottomSheet = DatePickerBottomSheet(
            maxYear = maxYear,
            onDateSelected = { year, month, day ->
                val selectedDate = Calendar.getInstance().apply {
                    set(year, month - 1, day)
                }
                val calculatedAge = calculateAge(selectedDate.time)
                if (calculatedAge < 18) {
                    ToastUtils.showToast("Age must be at least 18 years old")
                    return@DatePickerBottomSheet
                }
                val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                val birthdayStr = dateFormat.format(selectedDate.time)
                age = calculatedAge
                binding.tvAge.text = age.toString()
                viewModel.selectedBirthday = birthdayStr // 只赋值birthday用于上传
                validateSubmitButton()
            },
            birthday = birthday
        )

        datePickerBottomSheet.show(supportFragmentManager, "DatePickerBottomSheet")
    }

    /**
     * 展示选择国家弹窗
     */
    private fun showCountrySelectionDialog() {
        val bottomSheet = com.score.callmetest.ui.widget.CountrySelectBottomSheet(
            context = this,
            selectedCountry = binding.tvRegion.text.toString(),
            onCountrySelected = { selectedCountry ->
                binding.tvRegion.text = selectedCountry
               // binding.imageRegion.setImageResource(CountryUtils.getIconByEnName(selectedCountry))
                validateSubmitButton()
            }
        )
        bottomSheet.show(supportFragmentManager, "CountrySelectBottomSheet")
    }
    /**
     *验证必须的字段（nickname，birthday，country）
     */
    private fun validateAllFields(): Boolean {
        val nickname = binding.etNickname.text.toString().trim()
        val dateOfBirth = binding.tvAge.text.toString()
        val country = binding.tvRegion.text.toString()
        if (nickname.isEmpty()) {
            ToastUtils.showToast("Nickname cannot be empty")
            return false
        }
        if (dateOfBirth.isEmpty()) {
            ToastUtils.showToast("Date of Birth cannot be empty")
            return false
        }
        if (country.isEmpty()) {
            ToastUtils.showToast("Country cannot be empty")
            return false
        }
        return true
    }

    private fun syncUiToViewModel() {
        viewModel.selectedAvatarUri = selectedImageUri
        viewModel.selectedNickname = binding.etNickname.text.toString().trim()
        viewModel.selectedGender = if (binding.tvGender.text == "Male") 1 else 0
        // 不再赋值 birthday，birthday 只在选择生日时赋值
        viewModel.selectedCountry = binding.tvRegion.text.toString()
        viewModel.selectedAbout = binding.etSelfIntroduction.text.toString()
        viewModel.selectedMediaUris = photoList.toList()
    }

    private fun validateSubmitButton() {
        syncUiToViewModel()
        val nickname = viewModel.selectedNickname ?: ""
        val dateOfBirth = viewModel.selectedBirthday ?: ""
        val country = viewModel.selectedCountry ?: ""
        val enabled = viewModel.isProfileChanged() && nickname.isNotEmpty() && dateOfBirth.isNotEmpty() && country.isNotEmpty()
        binding.btnDone.isEnabled = enabled
        // 根据enabled动态设置背景和字体颜色
        if (enabled) {
            // 只改字体色
            binding.btnDone.setTextColor("#FF51F1".toColorInt())
            binding.btnDone.doOnPreDraw {
                binding.btnDone.background = DrawableUtils.createRoundRectDrawableWithStroke(
                    fillColor = Color.WHITE,
                    radius = binding.btnDone.height / 2f,
                    strokeColor = "#FFBFE5".toColorInt(),
                    strokeWidth = DisplayUtils.dp2pxInternal(1f)
                )
            }
            // 修改背景色
            (binding.btnDone.background as? GradientDrawable)?.setColor(0xFF7DF7EC.toInt())
        } else {
            binding.btnDone.setTextColor(0xFF4D000000.toInt())
            GlobalManager.setViewRoundBackground(binding.btnDone, Color.WHITE)
        }
    }

    private fun uploadAndSaveAll() {
        syncUiToViewModel()
        LoadingUtils.showLoading(this)
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                try {
                    viewModel.uploadAndSaveAll(
                        context = this@ProfileActivity,
                        showToast = {
                            LoadingUtils.dismissLoading()
                            showToast(it)
                        },
                        onSuccess = {
                            LoadingUtils.dismissLoading()
                            finish()
                        }
                    )
                } catch (e: Exception) {
                    LoadingUtils.dismissLoading()
                    showToast("Network exception: ${e.message}")
                }
            }
        }
    }

    private fun updateCharCount(text: String, max: Int = 300) {
        binding.tvCharCount.text = "${text.length}/$max"
    }

    private fun showToast(msg: String) {
        ToastUtils.showToast(msg)
    }

    private fun hideKeyboard() {
        val imm =  getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(binding.etSelfIntroduction.windowToken, 0)
        binding.etSelfIntroduction.clearFocus()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        AppPermissionManager.handleRequestPermissionsResult(this, requestCode, permissions, grantResults)
    }
    
    /**
     * 显示权限引导对话框
     */
    private fun showPermissionGuideDialog() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("🔧 Permission Settings Guide")
            .setMessage("It looks like you haven't enabled the relevant permissions yet. For the best user experience, we recommend enabling the following permissions:\n\n" +
                    "📷 Album permission: Upload avatar and photos\n" +
                    "📹 Camera permission: Video calls\n" +
                    "🎤 Microphone permission: Voice calls\n\n" +
                    "Click the button below and we'll open the settings page for you.")
            .setPositiveButton("⚙️ Go to Settings") { _, _ ->
                val intent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = android.net.Uri.fromParts("package", packageName, null)
                intent.data = uri
                startActivity(intent)
            }
            .setNegativeButton("❌ Later") { _, _ ->
                // 用户选择稍后再说，不做任何操作
            }
            .setCancelable(true)
            .create()
        
        dialog.show()
    }
}

