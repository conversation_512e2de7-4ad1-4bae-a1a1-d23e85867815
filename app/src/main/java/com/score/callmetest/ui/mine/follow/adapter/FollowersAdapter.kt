package com.score.callmetest.ui.mine.follow.adapter

import android.content.Intent
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication.Companion.context
import com.score.callmetest.R
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.FollowModel
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click
import de.hdodenhof.circleimageview.CircleImageView

class FollowersAdapter(
    private val onFollowClick: (FollowModel, Int) -> Unit,
    private val onStateChange: ((String, Boolean) -> Unit)? = null // 状态变化回调
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        private const val VIEW_TYPE_FOLLOWER = 0
        private const val VIEW_TYPE_BOTTOM = 1
    }
    private val followers = mutableListOf<FollowModel>()
    private var showBottom: Boolean = true

    // 本地关注状态管理，key为userId，value为是否关注
    private val localFollowStates = mutableMapOf<String, Boolean>()

    fun setShowBottom(show: Boolean) {
        if (this.showBottom != show) {
            this.showBottom = show
            notifyDataSetChanged()
        }
    }

    fun setData(newFollowers: List<FollowModel>) {
        followers.clear()
        followers.addAll(newFollowers)

        // 初始化本地关注状态，使用原始数据
        localFollowStates.clear()
        newFollowers.forEach { followModel ->
            localFollowStates[followModel.userId] = followModel.isFollows ?: false
        }

        notifyDataSetChanged()
    }

    override fun getItemCount(): Int = followers.size + if (showBottom) 1 else 0

    override fun getItemViewType(position: Int): Int {
        return if (showBottom && position == followers.size) VIEW_TYPE_BOTTOM else VIEW_TYPE_FOLLOWER
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_FOLLOWER -> ViewHolder(
                LayoutInflater.from(parent.context).inflate(
                    R.layout.item_fragment_followers, parent, false
                )
            )
            VIEW_TYPE_BOTTOM -> BottomViewHolder(
                LayoutInflater.from(parent.context).inflate(
                    R.layout.item_list_bottom, parent, false
                )
            )
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is ViewHolder && position < followers.size) {
            val followModel = followers[position]
            // 昵称
            holder.tvUsername.text = followModel.nickname ?: ""
            // 国家/地区
            val bgColor = Color.parseColor("#ff3ae2b0")
            val radiusDp = 7f
            holder.tvRegion.background = DrawableUtils.createRoundRectDrawableDp(bgColor, radiusDp)
            holder.tvRegion.text = followModel.country ?: ""
            // 头像
            val avatarUrl = followModel.avatarUrl ?:followModel.avatarThumbUrl ?: ""
            if (avatarUrl.isNotEmpty()) {
                Glide.with(holder.ivAvatar.context)
                    .load(avatarUrl)
                    .placeholder(R.drawable.placeholder)
                    .error(R.drawable.placeholder)
                    .into(holder.ivAvatar)
            } else {
                holder.ivAvatar.setImageResource(R.drawable.placeholder)
            }
            // 关注按钮状态 - 使用本地状态管理
            val cornerRadius = 14f
            val strokeColor = ContextCompat.getColor(context, R.color.follow_button_stroke)
            val strokeWidth = 1
            val isFollowing = localFollowStates[followModel.userId] ?: false

            updateFollowButtonUI(holder.tvAddFollow, isFollowing, cornerRadius, strokeColor, strokeWidth)
            // item点击事件
            holder.itemView.click {
                val position = holder.adapterPosition
                if (position != RecyclerView.NO_POSITION && position < followers.size) {
                    val followModel = followers[position]
                    val broadcasterModel = followModelToBroadcasterModel(followModel)
                    val context = holder.itemView.context
                    val intent = Intent(context, BroadcasterDetailActivity::class.java)
                    intent.putExtra("broadcaster_model", broadcasterModel)
                    context.startActivity(intent)
                }
            }
            // 关注按钮点击事件
            holder.tvAddFollow.click {
                val position = holder.adapterPosition
                if (position != RecyclerView.NO_POSITION && position < followers.size) {
                    val followModel = followers[position]

                    // 立即更新本地状态和UI
                    val currentState = localFollowStates[followModel.userId] ?: false
                    val newState = !currentState
                    localFollowStates[followModel.userId] = newState

                    // 立即更新UI
                    val cornerRadius = 14f
                    val strokeColor = ContextCompat.getColor(context, R.color.follow_button_stroke)
                    val strokeWidth = 1
                    updateFollowButtonUI(holder.tvAddFollow, newState, cornerRadius, strokeColor, strokeWidth)

                    // 通知状态变化
                    onStateChange?.invoke(followModel.userId, newState)

                    // 调用回调处理网络请求
                    onFollowClick(followModel, position)
                }
            }
        } else if (holder is BottomViewHolder) {
            holder.bind("Bottom")
        }
    }

    /**
     * 更新关注按钮UI状态
     */
    private fun updateFollowButtonUI(
        button: TextView,
        isFollowing: Boolean,
        cornerRadius: Float,
        strokeColor: Int,
        strokeWidth: Int
    ) {
        if (isFollowing) {
            button.text = "Following"
            val fillColor = ContextCompat.getColor(context, R.color.following_button_fill)
            button.background = DrawableUtils.createRoundRectDrawableDp(fillColor, cornerRadius)
        } else {
            button.text = "+ Follow"
            button.setTextColor(ContextCompat.getColor(context, R.color.follow_text_color))
            button.background = DrawableUtils.createRoundRectDrawableWithStrokeDp(
                ContextCompat.getColor(context, R.color.follow_button_fill),
                cornerRadius,
                strokeColor,
                strokeWidth
            )
        }
    }


    private fun followModelToBroadcasterModel(followModel: FollowModel): BroadcasterModel {
        return BroadcasterModel(
            userId = followModel.userId,
            nickname = followModel.nickname,
            avatar = followModel.avatar,
            avatarMapPath = null, // FollowModel没有，填null
            gender = followModel.gender,
            age = followModel.age,
            country = followModel.country,
            status = followModel.onlineStatus ?: CallStatus.OFFLINE,
            callCoins = followModel.unitPrice,
            unit = "min", // FollowModel没有，默认"min"
            videoMapPaths = null, // FollowModel没有，填null
            imageMapPaths = null, // FollowModel没有，填null
            followNum = null, // FollowModel没有，填null
            isFriend = followModel.mutualFlow,
            isMultiple = null, // FollowModel没有，填null
            about = followModel.about,
            grade = followModel.level,
            analysisLanguage = followModel.language,
            registerCountry = null, // FollowModel没有，填null
            isFakeBroadcaster = null, // FollowModel没有，填null
            isShowLowPrice = null, // FollowModel没有，填null
            isSignBroadcaster = followModel.isSignBroadcaster,
            showRoomVersion = followModel.showRoomVersion,
            broadcasterType = followModel.userType,
            isAnswer = null, // FollowModel没有，填null
            isLandScreen = null, // FollowModel没有，填null
            prioritizeDisplayVideo = null, // FollowModel没有，填null
            avatarThumbUrl = followModel.avatarThumbUrl,
            isVip = followModel.isVip ?: false,
            countdown = null // FollowModel没有，填null
        )
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivAvatar: CircleImageView = itemView.findViewById(R.id.iv_avatar)
        val tvUsername: TextView = itemView.findViewById(R.id.tv_username)
        val tvRegion: TextView = itemView.findViewById(R.id.tv_region)
        val tvAddFollow: TextView = itemView.findViewById(R.id.tv_add_follow)
    }

    inner class BottomViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvBottomText: TextView = itemView.findViewById(R.id.tv_bottom_text)
        fun bind(text: String) {
            tvBottomText.text = text
        }
    }
} 