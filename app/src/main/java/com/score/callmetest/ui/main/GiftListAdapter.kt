package com.score.callmetest.ui.main

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.R
import com.score.callmetest.network.GiftInfo
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click

open class GiftListAdapter(var data: List<GiftInfo>) :
    RecyclerView.Adapter<GiftListAdapter.GiftViewHolder>() {


    /** 在礼物发送上点击 */
    private var onGiftSentClick: ((GiftInfo) -> Unit)? = null

    fun setOnGiftSentClick(onGiftSentClick: ((GiftInfo) -> Unit)?) {
        this.onGiftSentClick = onGiftSentClick
    }

    var selectedIndex: Int = 0
        set(value) {
            val old = field
            if (old != value) {
                field = value
                notifyItemChanged(old)
                notifyItemChanged(field)
            }
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GiftViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_gift, parent, false)
        return GiftViewHolder(view)
    }

    override fun onBindViewHolder(holder: GiftViewHolder, position: Int) {
        val item = data[position]

        /*val giftUri = item.iconThumbPath ?: item.iconPath ?: run {
            // 通过name来寻找本地png
            val name = item.code ?: ""
            CustomUtils.getGiftResIdByName(name)
        }*/

        // 确定只使用本地礼物资源
        val name = item.code ?: ""
        val giftUri = CustomUtils.getGiftResIdByName(name)


        // 加载图片
        GlideUtils.load(
            view =holder.ivGift,
            url = giftUri,
            placeholder = R.drawable.gift_placehold,
            error = R.drawable.gift_placehold,
        )
        // 金币价格
        holder.tvGiftPrice.text = if (item.coinPrice != null) "${item.coinPrice.toInt()}" else ""
        val selected = position == selectedIndex
        holder.itemView.isSelected = selected
        holder.coinLayout.visibility = if (selected) View.INVISIBLE else View.VISIBLE
        holder.sendButton.visibility = if (selected) View.VISIBLE else View.INVISIBLE

        // 设置主内容区域背景
        val r = DisplayUtils.dp2px(10f).toFloat()
        val radii = floatArrayOf(0f, 0f, 0f, 0f, r, r, r, r)
        holder.sendButton.background =
            DrawableUtils.createRoundRectDrawable("#7DF7EC".toColorInt(), radii)

        holder.itemView.click {
            val adapterPosition = holder.absoluteAdapterPosition
            if (adapterPosition != RecyclerView.NO_POSITION) {
                selectedIndex = adapterPosition
            }
        }
        holder.sendButton.click {
            onGiftSentClick?.invoke(item)
        }
    }

    override fun getItemCount(): Int = data.size

    class GiftViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivGift: ImageView = itemView.findViewById(R.id.iv_gift)
        val tvGiftPrice: TextView = itemView.findViewById(R.id.tv_gift_price)
        val coinLayout: ViewGroup = itemView.findViewById(R.id.coin_layout)
        val sendButton: View = itemView.findViewById(R.id.send)
    }
} 