package com.score.callmetest.ui.mine.blockList

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.score.callmetest.network.BlockListItem
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.launch

class BlockListViewModel : ViewModel() {
    private val _blockList = MutableLiveData<List<BlockListItem>>()
    val blockList: LiveData<List<BlockListItem>> = _blockList

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    fun fetchBlockList(limit: Int = 15, page: Int = 1) {
        viewModelScope.launch {
            try {
                val request = com.score.callmetest.network.UserBlockListRequest(limit = limit, page = page)
                val response = RetrofitUtils.apiService.getBlockList(request)
                if (response.success && response.data != null) {
                    _blockList.value = response.data ?: emptyList()
                    response.data.toString().logAsTag(this.javaClass.name + "fetchBlockList: ")
                } else {
                    _blockList.value = emptyList()
                    _error.value = response.msg ?: "获取屏蔽列表失败"
                    response.toString().logAsTag(this.javaClass.name + " fetchBlockList: fail")
                }
            } catch (e: Exception) {
                e.toString().logAsTag(this.javaClass.name + " fetchBlockList: exception")
                _error.value = e.message ?: "网络异常"
            }
        }
    }

    // 拉黑
    fun block(userId: String, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val resp = kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.IO) {
                    RetrofitUtils.apiService.insertComplainRecord(
                        com.score.callmetest.network.ComplainInsertRecordRequest(
                            broadcasterId = userId,
                            channelName = null,
                            complainCategory = "Block",
                            complainSub = null,
                            isAudit = false,
                            reason = null,
                            snapshotPath = null
                        )
                    )
                }
                if (resp.code == 0) {
                    // userInfo?.isBlock = true // 这里没有userInfo对象，视具体业务可补充
                    callback(true)
                } else {
                    callback(false)
                }
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    // 取消拉黑
    fun unblock(userId: String, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val resp = kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.IO) {
                    RetrofitUtils.apiService.removeBlock(
                        com.score.callmetest.network.RemoveBlockRequest(blockUserId = userId)
                    )
                }
                if (resp.code == 0) {
                    // userInfo?.isBlock = false // 这里没有userInfo对象，视具体业务可补充
                    callback(true)
                } else {
                    callback(false)
                }
            } catch (e: Exception) {
                callback(false)
            }
        }
    }
}