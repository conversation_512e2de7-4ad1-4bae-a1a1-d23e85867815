package com.score.callmetest.ui.widget

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.score.callmetest.R

class MineFunctionItemView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AlphaRelativeLayout(context, attrs, defStyleAttr) {

    private val ivIcon: ImageView
    private val tvTitle: TextView
    private val tvTip: TextView
    private val ivArrow: ImageView

    init {
        LayoutInflater.from(context).inflate(R.layout.item_mine_function, this, true)

        ivIcon = findViewById(R.id.iv_icon)
        tvTitle = findViewById(R.id.tv_title)
        tvTip = findViewById(R.id.tv_tip)
        ivArrow = findViewById(R.id.right_arrow)

        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.MineFunctionItemView)

        val iconResId = typedArray.getResourceId(R.styleable.MineFunctionItemView_mfi_icon, 0)
        if (iconResId != 0) {
            ivIcon.setImageDrawable(ContextCompat.getDrawable(context, iconResId))
        }

        val title = typedArray.getString(R.styleable.MineFunctionItemView_mfi_title)
        tvTitle.text = title

        val tip = typedArray.getString(R.styleable.MineFunctionItemView_mfi_tip)
        tvTip.text = tip

        val showArrow = typedArray.getBoolean(R.styleable.MineFunctionItemView_mfi_show_arrow, true)
        ivArrow.isVisible = showArrow

        typedArray.recycle()
    }

    fun setIcon(resId: Int) {
        ivIcon.setImageResource(resId)
    }

    fun setTitle(title: String) {
        tvTitle.text = title
    }

    fun setTip(tip: String) {
        tvTip.text = tip
    }
} 