package com.score.callmetest.ui.mine.follow

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.databinding.FragmentFollowContentBinding
import com.score.callmetest.network.FollowModel
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.mine.follow.adapter.FollowersAdapter
import com.score.callmetest.util.ToastUtils
import timber.log.Timber


/**
 * 粉丝列表页面
 * 展示用户的粉丝列表
 */
class FollowersFragment : BaseFragment<FragmentFollowContentBinding, FollowViewModel>() {

    // recyclerview 相关
    private lateinit var mAdapter: FollowersAdapter
    private val mLayoutManager by lazy { LinearLayoutManager(context) }

    // 是否需要滚动到顶部的标志
    private var mNeedScrollToTop = false

    // 状态变化记录，用于批量更新
    private val initialFollowStates = mutableMapOf<String, Boolean>() // 初始状态
    private val currentFollowStates = mutableMapOf<String, Boolean>() // 当前状态
    private val pendingStateChanges = mutableMapOf<String, Boolean>() // 待提交的状态变化

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentFollowContentBinding = FragmentFollowContentBinding.inflate(inflater, container, false)

    override fun getViewModelClass(): Class<FollowViewModel> = FollowViewModel::class.java

    override fun initView() {
        super.initView()
        setupRecyclerView()
        setupSwipeRefresh()
    }
    
    private fun setupRecyclerView() {
        mAdapter = FollowersAdapter(
            onFollowClick = { followModel, position ->
                onFollowButtonClick(followModel, position)
            },
            onStateChange = { userId, newState ->
                // 更新当前状态记录
                currentFollowStates[userId] = newState

                // 记录待提交的状态变化
                val initialState = initialFollowStates[userId] ?: false
                if (newState != initialState) {
                    pendingStateChanges[userId] = newState
                } else {
                    pendingStateChanges.remove(userId)
                }
            }
        )
        binding.recyclerView.apply {
            adapter = mAdapter
            layoutManager = mLayoutManager

            // 添加滚动监听，实现加载更多
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 只有向下滚动时才检查
                    if (dy > 0) {
                        val visibleItemCount = mLayoutManager.childCount
                        val totalItemCount = mLayoutManager.itemCount
                        val firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition()

                        // 当滚动到倒数第5个item时开始加载更多
                        if (!viewModel.isFollowersLoadingMore() &&
                            viewModel.hasMoreFollowersData() &&
                            (visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 5) {
                            loadMoreFollowers()
                        }
                    }
                }
            })
        }
    }

    /**
     * 更新底部项显示状态
     */
    private fun updateBottomItemVisibility() {
        binding.recyclerView.post {
            // 只有当有更多数据时才显示底部加载项
            val shouldShowBottom = viewModel.hasMoreFollowersData() && mAdapter.itemCount > 0
            mAdapter.setShowBottom(shouldShowBottom)
        }
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            // 下拉刷新
            setOnRefreshListener {
                // 显示刷新动画
                isRefreshing = true
                // 设置需要滚动到顶部的标志
                mNeedScrollToTop = true
                // 重新加载数据（刷新）
                loadFollowersList(isRefresh = true)
            }
        }
    }

    override fun initData() {
        super.initData()
        setupObservers()
        binding.emptyView.inflate()

    }

    private fun loadFollowersList(isRefresh: Boolean = true) {
        // 调用ViewModel加载粉丝列表
        viewModel.loadFollowersList(isRefresh)
    }

    private fun loadMoreFollowers() {
        // 加载更多粉丝数据
        viewModel.loadFollowersList(isRefresh = false)
    }
    
    private fun setupObservers() {

        // 观察粉丝列表数据变化
        viewModel.followersList.observe(viewLifecycleOwner) { followersList ->
            // 显示/隐藏空视图
            emptyView(followersList.isNullOrEmpty())

            // 提交新列表前保存当前滚动位置
            val firstVisiblePosition = mLayoutManager.findFirstVisibleItemPosition()

            // 记录初始状态（只在首次加载时记录）
            if (initialFollowStates.isEmpty() && !followersList.isNullOrEmpty()) {
                followersList.forEach { followModel ->
                    val isFollowing = followModel.isFollows ?: false
                    initialFollowStates[followModel.userId] = isFollowing
                    currentFollowStates[followModel.userId] = isFollowing
                }
            }

            // 更新适配器数据
            mAdapter.setData(followersList ?: emptyList())

            // 数据加载完成后，停止刷新动画
            binding.swipeRefreshLayout.isRefreshing = false

            // 只有在需要滚动到顶部时才滚动（下拉刷新时）
            if (mNeedScrollToTop) {
                binding.recyclerView.scrollToPosition(0)
                mNeedScrollToTop = false
            } else if (firstVisiblePosition == 0) {
                // 如果原本就在顶部，确保仍然在顶部
                binding.recyclerView.scrollToPosition(0)
            }

            // 列表更新后检查底部项显示状态
            updateBottomItemVisibility()
        }
        
        // 观察加载状态 - 只有在刷新时才显示顶部加载动画
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // 只有在不是加载更多的情况下才显示顶部加载动画
            if (!viewModel.isFollowersLoadingMore()) {
                binding.swipeRefreshLayout.isRefreshing = isLoading
                if (isLoading) {
                    mNeedScrollToTop = true
                }
            }
        }
        
        // 观察错误信息
        viewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            if (!errorMessage.isNullOrEmpty()) {
                Timber.tag("follow").e("Error loading followers: $errorMessage")
                // 错误发生时，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false
            }
            emptyView(viewModel.followersList.value.isNullOrEmpty())
        }
    }

    /**
     * 显示/隐藏EmptyView
     */
    private fun emptyView(isEmpty: Boolean) {
        if (isEmpty) {
            binding.emptyView.visibility = View.VISIBLE
            binding.recyclerView.visibility = View.GONE
        } else {
            binding.emptyView.visibility = View.GONE
            binding.recyclerView.visibility = View.VISIBLE
        }
    }

    /**
     * 处理关注/取关按钮点击
     * 只显示即时反馈，状态管理已在adapter回调中处理
     */
    private fun onFollowButtonClick(followModel:FollowModel, position: Int) {
        // 获取当前状态
        val newState = currentFollowStates[followModel.userId] ?: false

        // 显示即时反馈
        val message = if (newState) "已添加到关注列表" else "已从关注列表移除"
        ToastUtils.showShortToast(message)
    }

    override fun onResume() {
        super.onResume()
        // 页面可见时检查底部项显示状态
        updateBottomItemVisibility()
        // 进入页面开始更新列表数据
        loadFollowersList(isRefresh = true)
    }

    override fun onPause() {
        super.onPause()
        // 页面不可见时，批量处理状态变化
        processPendingStateChanges()
    }

    /**
     * 批量处理待提交的状态变化
     */
    private fun processPendingStateChanges() {
        if (pendingStateChanges.isEmpty()) {
            return
        }

        // 复制待处理的变化，避免在处理过程中被修改
        val changesToProcess = pendingStateChanges.toMap()
        pendingStateChanges.clear()

        // 批量处理状态变化
        changesToProcess.forEach { (userId, newState) ->
            if (newState) {
                // 执行关注操作
                viewModel.followUser(userId) { success ->
                    if (!success) {
                        // 失败时恢复到初始状态
                        val initialState = initialFollowStates[userId] ?: false
                        currentFollowStates[userId] = initialState

                        // 如果Fragment还在活跃状态，显示错误信息
                        if (isAdded && !isDetached) {
                            requireActivity().runOnUiThread {
                                ToastUtils.showShortToast("关注失败，已恢复原状态")
                            }
                        }
                    } else {
                        // 成功时更新初始状态
                        initialFollowStates[userId] = newState
                    }
                }
            } else {
                // 执行取消关注操作
                viewModel.unfollowUser(userId) { success ->
                    if (!success) {
                        // 失败时恢复到初始状态
                        val initialState = initialFollowStates[userId] ?: false
                        currentFollowStates[userId] = initialState

                        // 如果Fragment还在活跃状态，显示错误信息
                        if (isAdded && !isDetached) {
                            requireActivity().runOnUiThread {
                                ToastUtils.showShortToast("取消关注失败，已恢复原状态")
                            }
                        }
                    } else {
                        // 成功时更新初始状态
                        initialFollowStates[userId] = newState
                    }
                }
            }
        }
    }
    
    
} 