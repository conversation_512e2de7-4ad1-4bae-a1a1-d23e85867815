package com.score.callmetest.ui.broadcaster

import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.net.Uri
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.bumptech.glide.Glide
import com.google.android.flexbox.FlexboxLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.CallStatus
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityBroadcasterDetailBinding
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.TranslateManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterExtraInfo
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.GetGiftCountItem
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.main.BroadcasterGiftListActivity
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.widget.CoinRechargeDialog
import com.score.callmetest.ui.widget.CommonActionBottomSheet
import com.score.callmetest.ui.widget.FollowEvent
import com.score.callmetest.ui.widget.Helper.PhotoPagerHelper
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.LanguageUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.Runnable


@SuppressLint("SetTextI18n")
class BroadcasterDetailActivity :
    BaseActivity<ActivityBroadcasterDetailBinding, BroadcasterDetailViewModel>() {
    var broadcaster: BroadcasterModel? = null
    private val statusHandler = android.os.Handler(android.os.Looper.getMainLooper())
    private val statusRefreshRunnable = object : Runnable {
        override fun run() {
            if (!viewModel.userInfo?.userId.isNullOrEmpty()) {
                UserInfoManager.loadOnlineStatus(
                    lifecycleScope, viewModel.userInfo?.userId.toString()
                ) { status, error ->
                    runOnUiThread {
                        if (error == null && status != null) {
                            updateStatus(status)
                        }
                    }
                }
            }
            statusHandler.postDelayed(this, 3000)
        }
    }
    private var isTranslationShown = false
    private var translatedText: String? = null
    private var introOriginal: String? = null

    override fun getViewBinding(): ActivityBroadcasterDetailBinding {
        return ActivityBroadcasterDetailBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = BroadcasterDetailViewModel::class.java

    override fun initView() {
        broadcaster =
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                intent.getParcelableExtra("broadcaster_model", BroadcasterModel::class.java)
            } else {
                @Suppress("DEPRECATION") intent.getParcelableExtra<BroadcasterModel>("broadcaster_model")
            }
        if (broadcaster == null) {
            finish()
            return
        }
        initBasicUI()
        initMainContentLayout()
        // 关注按钮初始状态
        binding.followLayout.setIsFriend(false)
    }

    private fun initBasicUI() {
        GlobalManager.setViewRoundBackground(binding.photoCount, "#33000000".toColorInt())
        GlobalManager.setViewRoundBackground(binding.statusLayout, "#33000000".toColorInt())

        GlobalManager.setViewRoundBackground(
            binding.ageLayout,
            ContextCompat.getColor(this@BroadcasterDetailActivity, R.color.age_bg)
        )

        GlobalManager.setViewRoundBackground(
            binding.country,
            ContextCompat.getColor(this@BroadcasterDetailActivity, R.color.country_bg)
        )
        GlobalManager.setViewRoundBackground(
            binding.lineLanguage,
            ContextCompat.getColor(this@BroadcasterDetailActivity, R.color.language_bg)
        )
        binding.videoCallLayout.elevation = DisplayUtils.dp2px(3f).toFloat()
        binding.btnChat.elevation = DisplayUtils.dp2px(5f).toFloat()
    }

    private fun initMainContentLayout() {
        // 设置主内容区域背景
        val r = DisplayUtils.dp2px(18f).toFloat()
        val radii = floatArrayOf(r, r, r, r, 0f, 0f, 0f, 0f)
        binding.mainContentLayout.background =
            DrawableUtils.createRoundRectDrawable(Color.WHITE, radii)
        // 限制BottomSheet最大高度为屏幕高度-100dp
        val maxExpandedHeight = DeviceUtils.getScreenHeight(this) - DisplayUtils.dp2px(100f)
        binding.mainContentLayout.post {
            val params = binding.mainContentLayout.layoutParams
            params.height = maxExpandedHeight
            binding.mainContentLayout.layoutParams = params
            // 设置BottomSheetBehavior初始高度
            val avatarHeight = DeviceUtils.getScreenHeight(this) * 3 / 5
            binding.avatarLayout.layoutParams.height = avatarHeight
            binding.avatarLayout.requestLayout()
            val behavior = BottomSheetBehavior.from(binding.mainContentLayout)
            behavior.peekHeight =
                DeviceUtils.getScreenHeight(this) - avatarHeight + DisplayUtils.dp2px(18f)
            behavior.state = BottomSheetBehavior.STATE_COLLAPSED
            // 限制滑动到顶部100dp
            behavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    // 防止完全展开
                    if (newState == BottomSheetBehavior.STATE_EXPANDED) {
                        val location = IntArray(2)
                        bottomSheet.getLocationOnScreen(location)
                        val top = location[1]
                        val minTop = DisplayUtils.dp2px(100f)
                        if (top < minTop) {
                            bottomSheet.y = minTop.toFloat()
                        }
                    }
                }

                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    val location = IntArray(2)
                    bottomSheet.getLocationOnScreen(location)
                    val top = location[1]
                    val minTop = DisplayUtils.dp2px(100f)
                    if (top < minTop) {
                        bottomSheet.y = minTop.toFloat()
                    }
                }
            })
        }
    }

    override fun initData() {
        if (StrategyManager.isReviewPkg()) {
            binding.giftArrow.visibility = View.GONE
//            binding.statusLayout.visibility = View.GONE
        }

        binding.nickname.text = broadcaster?.nickname
        binding.age.text = broadcaster?.age?.toString() ?: ""
        binding.country.text = broadcaster?.country ?: ""
        updateStatus(broadcaster?.status.toString())

        // 回调方式加载所有数据
        viewModel.loadAll(
            userId = broadcaster!!.userId,
            onOnlineStatus = { status, error ->
                runOnUiThread {
                    if (error == null && status != null) {
                        status.toString().logAsTag(this.javaClass.name + "status: ")
                        updateStatus(status)
                    }
                }
            },
            onUserInfo = { userInfo, error ->
                runOnUiThread {
                    if (error == null && userInfo != null) {
                        updateBaseInfo(userInfo)
                        binding.followLayout.setIsFriend(userInfo.isFriend)
                        userInfo.toString().logAsTag(this.javaClass.name + "userInfo: ")
                    }
                }
            },
            onExtraInfo = { extraInfo, error ->
                runOnUiThread {
                    if (error == null && extraInfo != null) {
                        updateExtraLabels(extraInfo)
                        extraInfo.toString().logAsTag(this.javaClass.name + "extraInfo: ")
                    }
                }
            },
            onGiftCount = { giftInfo, error ->
                runOnUiThread {
                    if (error == null && giftInfo != null) {
                        updateExtraGift(giftInfo.normalGiftNum)
                        giftInfo.toString().logAsTag(this.javaClass.name + "giftInfo: ")
                    }
                }
            })
    }

    override fun initListener() {
        EventBus.observe(this, FollowEvent::class.java) { event ->
            binding.followLayout.setIsFriend(event.isFriend)
        }
        binding.back.click {
            finish()
        }
        binding.more.click {
            showMoreOptionsDialog()
        }
        binding.btnChat.click {
            // 私聊
            ChatActivity.start(this, viewModel.userInfo)

//            CallEndFreeFragmentDialog(viewModel.userInfo!!).show(supportFragmentManager, "CallEndFreeFragmentDialog")
        }
        binding.followLayout.click {
            followUser()
        }
        if (!StrategyManager.isReviewPkg()) {
            binding.giftListLayout.click {
                viewModel.giftCount?.let {
                    val intent = Intent(this, BroadcasterGiftListActivity::class.java)
                    intent.putExtra("gift_response", it)
                    startActivity(intent)
                }
                // fixme: Test
//            GiftDialogFragment().show(supportFragmentManager, "gift_dialog")
            }
            binding.giftTitle.click {
                viewModel.giftCount?.let {
                    val intent = Intent(this, BroadcasterGiftListActivity::class.java)
                    intent.putExtra("gift_response", it)
                    startActivity(intent)
                }
            }
        }

        binding.videoCallLayout.click {
            // 检查摄像头和麦克风权限
            AppPermissionManager.checkAndRequestCameraMicrophonePermission(this, onGranted = {
                if (!StrategyManager.isReviewPkg()) {
                    // 判断金币是否足够
                    val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
                    val unitPrice = viewModel.userInfo?.unitPrice ?: 0
                    if (availableCoins < unitPrice) {
                        // 弹出金币充值弹窗（新版：自动拉取商品数据）
                        CoinRechargeDialog().show(supportFragmentManager, "coin_recharge")
                        return@checkAndRequestCameraMicrophonePermission
                    }
                }

                if (!SocketManager.instance.isConnected()) {
                    ToastUtils.showToast("Long connection network is offline")
                    return@checkAndRequestCameraMicrophonePermission
                }

                // 权限授权成功，在获取一次在线状态
                UserInfoManager.loadOnlineStatus(
                    lifecycleScope, viewModel.userInfo?.userId.toString()
                ) { status, error ->
                    runOnUiThread {
                        if (error == null && status != null) {
                            if (CallStatus.ONLINE != status && CallStatus.AVAILABLE != status) {
                                updateStatus(status)
                                return@runOnUiThread
                            }
                            VideoCallActivity.startOutgoing(
                                context = this@BroadcasterDetailActivity,
                                userId = viewModel.userInfo?.userId!!,
                                avatarUrl = viewModel.userInfo?.avatarUrl.toString(),
                                nickname = viewModel.userInfo?.nickname.toString(),
                                age = viewModel.userInfo?.age.toString(),
                                country = viewModel.userInfo?.country.toString(),
                                unitPrice = viewModel.userInfo?.unitPrice.toString()
                            )
                        }
                    }
                }
            }, onDenied = {
                AppPermissionManager.incrementPermissionCheckCount()
                if (AppPermissionManager.shouldShowPermissionGuide(this)) {
                    showPermissionGuideDialog()
                } else {
                    ToastUtils.showToast("Camera and microphone permissions are required for video calls")
                }
            })
        }
        binding.mainContentLayout.click {
            // 拦截点击事件
        }
        // 翻译按钮点击展开/收起
        binding.translateLayout.click {
            if (isTranslationShown) {
                binding.introTranslated.visibility = View.GONE
                isTranslationShown = false
            } else if (!translatedText.isNullOrBlank()) {
                binding.introTranslated.text = translatedText
                binding.introTranslated.visibility = View.VISIBLE
                isTranslationShown = true
            } else if (!introOriginal.isNullOrBlank()) {
                // 手动触发翻译
                val userLang = LanguageUtils.getAppLanguage(this)
                TranslateManager.translate(introOriginal!!, userLang) { result ->
                    runOnUiThread {
                        translatedText = result
                        if (!result.isNullOrBlank()) {
                            binding.introTranslated.text = result
                            binding.introTranslated.visibility = View.VISIBLE
                            isTranslationShown = true
                        } else {
                            binding.introTranslated.visibility = View.GONE
                        }
                    }
                }
            }
        }
        // 点击翻译内容收起
        binding.introTranslated.click {
            binding.introTranslated.visibility = View.GONE
            isTranslationShown = false
        }
    }

    fun updateBaseInfo(userInfo: UserInfo) {
        updatePhotoPager(userInfo)
        binding.apply {
            intro.text = userInfo.about ?: ""
            introOriginal = userInfo.about ?: ""
            nickname.text = userInfo.nickname
            age.text = userInfo.age?.toString() ?: ""
            country.text = userInfo.country ?: ""
            tvLanguage.text = userInfo.language?.uppercase() ?: ""

            GlobalManager.setViewRoundBackground(
                giftIndicator, resources.getColor(R.color.tab_indicator_color)
            )

            // 自动翻译逻辑
            val context = this@BroadcasterDetailActivity
            val userLang = LanguageUtils.getAppLanguage(context)

            // 检查自动翻译开关
            val autoTranslateEnabled =
                SharePreferenceUtil.getBoolean(Constant.AUTO_TRANSLATE, true, "settings")

            // 假设 userInfo.aboutLang 表示介绍的原始语言（如有）
            if (autoTranslateEnabled && introOriginal != null && introOriginal!!.isNotBlank()) {
                // 自动翻译
                TranslateManager.translate(introOriginal!!, userLang) { result ->
                    runOnUiThread {
                        translatedText = result
                        if (!result.isNullOrBlank()) {
                            binding.translate.visibility = View.VISIBLE
                            binding.introTranslated.text = result
                            binding.introTranslated.visibility = View.VISIBLE
                            isTranslationShown = true
                        } else {
                            binding.introTranslated.visibility = View.GONE
                        }
                    }
                }
            } else {
                binding.introTranslated.visibility = View.GONE
                binding.translate.visibility = View.VISIBLE
                isTranslationShown = false
            }
        }
    }

    // 更新轮播
    fun updatePhotoPager(userInfo: UserInfo) {
        val realImageUrls = mutableListOf<String>()
        if (!userInfo.avatarThumbUrl.isNullOrEmpty()) {
            realImageUrls.add(userInfo.avatarThumbUrl)
        } else if (!userInfo.avatar.isNullOrEmpty()) {
            realImageUrls.add(userInfo.avatar)
        }
        if (!userInfo.mediaList.isNullOrEmpty()) {
            realImageUrls.addAll(userInfo.mediaList.mapNotNull { it.mediaUrl })
        }

        // 使用PhotoPagerHelper设置轮播，支持自动轮播
        PhotoPagerHelper.setupPhotoPager(
            viewPager = binding.photoPager,
            imageUrls = realImageUrls,
            context = this,
            enableAutoScroll = true,
            autoScrollInterval = 5000L
        )

        // 设置指示器
        if (realImageUrls.size > 1) {
            binding.photoCount.visibility = View.VISIBLE
            binding.photoCount.text = "1/${realImageUrls.size}"
            binding.photoPager.registerOnPageChangeCallback(object : OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    val realIndex = (position - 1 + realImageUrls.size) % realImageUrls.size
                    binding.photoCount.text = "${realIndex + 1}/${realImageUrls.size}"
                }
            })
        } else {
            binding.photoCount.text = "1/1"
            binding.photoCount.visibility = View.GONE
        }
    }

    fun updateExtraLabels(extra: BroadcasterExtraInfo) {
        if (StrategyManager.isReviewPkg()) {
            binding.tagsContainer.visibility = View.GONE
            return
        }
        val container = binding.tagsContainer
        container.removeAllViews()

        // 1. 添加第一个带虚线边框和图标的按钮
        container.addView(ImageView(this).apply {
            setImageResource(R.drawable.labels) // 用已有的labels图标.
            layoutParams = FlexboxLayout.LayoutParams(
                DisplayUtils.dp2px(28f), DisplayUtils.dp2px(28f)
            ).apply {
                setMargins(0, 0, 0, 16)
            }
            background = DrawableUtils.createRoundRectDashedDrawable(0, 0f, Color.BLACK, 1, 1f, 3f)
        })

        // 2. 添加labelsList标签
        extra.labelsList?.mapNotNull { label ->
            val parts = label.split(":")
            if (parts.size == 2) {
                val name = parts[0].trim()
                val count = parts[1].trim().toIntOrNull() ?: 0
                name to count
            } else null
        }?.sortedByDescending { it.second }?.take(4)?.forEach { (name, _) ->
            container.addView(TextView(this).apply {
                text = name
                setTextColor(Color.BLACK)
                textSize = 14f
                val paddingHorizontal = DisplayUtils.dp2px(14f)
                val paddingVertical = DisplayUtils.dp2px(9f)
                setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical)
                setTypeface(Typeface.create("sans-serif", Typeface.NORMAL));
                GlobalManager.setViewRoundBackground(this, "#F3F5FA".toColorInt())
                layoutParams = FlexboxLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT
                ).apply {
                    setMargins(16, 0, 0, 16)
                }
            })
        }
    }

    fun updateExtraGift(giftList: List<GetGiftCountItem>?) {
        val giftListLayout = binding.giftListLayout
        giftListLayout.removeAllViews()
        if (giftList.isNullOrEmpty()) {
            binding.giftTitle.visibility = View.GONE
            giftListLayout.visibility = View.GONE
            return
        }
        giftListLayout.visibility = View.VISIBLE
        binding.giftTitle.visibility = View.VISIBLE
        val maxGifts = 4
        val context = this
        val sortedGifts = giftList.sortedByDescending { it.num ?: 0 }.take(maxGifts)
        val itemMargin = DisplayUtils.dp2px(8f)
        val itemWidth = if (giftListLayout.width > 0) {
            (giftListLayout.width - giftListLayout.paddingStart - giftListLayout.paddingEnd - itemMargin * (maxGifts - 1)) / maxGifts
        } else {
            DisplayUtils.dp2px(74f) // fallback
        }
        for ((idx, item) in sortedGifts.withIndex()) {
            val itemView = LayoutInflater.from(context)
                .inflate(R.layout.item_broadcaster_detail_gift, giftListLayout, false)
            DrawableUtils.setRoundRectBackground(
                itemView, ("#FFF3F5FA").toColorInt(), DisplayUtils.dp2pxInternal(13f).toFloat()
            )
            val ivGift = itemView.findViewById<ImageView>(R.id.iv_gift)
            val tvGiftCount = itemView.findViewById<TextView>(R.id.tv_gift_count)
            // 加载图片
            Glide.with(context).load(item.url).placeholder(R.drawable.gift_placehold)
                .into(ivGift)
            // 设置数量
            tvGiftCount.text = "x${item.num ?: 0}"
            // 设置宽度和间距
            val params = itemView.layoutParams as LinearLayout.LayoutParams
            params.width = itemWidth
            if (idx > 0) params.leftMargin = itemMargin
            itemView.layoutParams = params
            giftListLayout.addView(itemView)
        }
    }

    fun updateStatus(status: String) {
        Log.d(this.javaClass.name, "status: ${status}")

        GlobalManager.setViewRoundBackground(
            binding.statusIndicator, GlobalManager.getStatusColor(status)
        )
        binding.statusText.text = status

        val btnVideoCall = binding.btnVideoCall
        val videoCallLayout = binding.videoCallLayout
        val videoCallTip = binding.videoCallTip
        val firstLineTip = binding.firstLineTip
        val secondLine = binding.secondLine
        //审核模式下隐藏
        if (StrategyManager.isReviewPkg()) {
            secondLine.visibility = View.GONE
        }

        when (status) {
            CallStatus.ONLINE, CallStatus.AVAILABLE -> {
                videoCallLayout.doOnPreDraw {
                    videoCallLayout.background = DrawableUtils.createGradientDrawable(
                        colors = GlobalManager.getMainButtonBgGradientColors(),
                        radius = videoCallLayout.height / 2f
                    )
                }

                videoCallTip.text = "Video Call"
                videoCallTip.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));

                firstLineTip.visibility = View.GONE
                val price = viewModel.userInfo?.unitPrice ?: broadcaster?.callCoins
                secondLine.text = "(\uD83D\uDCB0 ${price}/min)"

                val parser = SVGAParser(this)
                parser.decodeFromAssets("info_video.svga", object : SVGAParser.ParseCompletion {
                    override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                        btnVideoCall.setImageDrawable(SVGADrawable(videoItem))
                        btnVideoCall.loops = 0 // 无限循环
                        btnVideoCall.startAnimation()
                    }

                    override fun onError() {}
                })
                btnVideoCall.setPadding(0, 0, 0, 0)
            }

            else -> {
                // 灰色背景和不可用样式
                val videoBtnPadding = DisplayUtils.dp2pxInternal(3f)
                btnVideoCall.setPadding(
                    videoBtnPadding, videoBtnPadding, videoBtnPadding, videoBtnPadding
                )
                GlobalManager.setViewRoundBackground(videoCallLayout, 0xFFD1D2DE.toInt())
                btnVideoCall.stopAnimation(true)
                btnVideoCall.setImageResource(R.drawable.video_disabled)
                videoCallTip.text = "Video Call"
                val price = viewModel.userInfo?.unitPrice ?: broadcaster?.callCoins
                firstLineTip.text = "(\uD83D\uDCB0 ${price}/min)"
                firstLineTip.visibility = View.VISIBLE

                //审核模式下隐藏
                if (StrategyManager.isReviewPkg()) {
                    firstLineTip.visibility = View.GONE
                }

                secondLine.text = "Unacceptable Now"
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        PhotoPagerHelper.cleanup()
        viewModel.close()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        AppPermissionManager.handleRequestPermissionsResult(
            this, requestCode, permissions, grantResults
        )
    }

    /**
     * 显示权限引导对话框
     */
    private fun showPermissionGuideDialog() {
        val dialog = AlertDialog.Builder(this).setTitle("🔧 Permission Settings Guide").setMessage(
            "It looks like you haven't enabled the relevant permissions yet. For the best user experience, we recommend enabling the following permissions:\n\n" + "📷 Album permission: Upload avatar and photos\n" + "📹 Camera permission: Video calls\n" + "🎤 Microphone permission: Voice calls\n\n" + "Click the button below and we'll open the settings page for you."
        ).setPositiveButton("⚙️ Go to Settings") { _, _ ->
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            val uri = Uri.fromParts("package", packageName, null)
            intent.data = uri
            startActivity(intent)
        }.setNegativeButton("❌ Later") { _, _ ->
            // 用户选择稍后再说，不做任何操作
        }.setCancelable(true).create()

        dialog.show()
    }

    override fun onResume() {
        super.onResume()
        statusHandler.post(statusRefreshRunnable)
    }

    override fun onPause() {
        super.onPause()
        statusHandler.removeCallbacks(statusRefreshRunnable)
    }

    private fun showMoreOptionsDialog() {
        val isFollowed = viewModel.userInfo?.isFriend == true
        val isBlocked = viewModel.userInfo?.isBlock == true

        // 使用通用底部对话框组件
        val sheet = CommonActionBottomSheet.builder()

        // 只有已关注时才显示取消关注选项
        if (isFollowed) {
            sheet.addAction(getString(R.string.str_btn_unfollow)) {
                unfollowUser()
            }
        }

        // 根据拉黑状态显示不同的选项
        if (isBlocked) {
            sheet.addAction(getString(R.string.str_btn_unblock)) {
                unblockUser()
                ToastUtils.showToast("Unblock successfully")
            }
        } else {
            sheet.addAction(getString(R.string.str_btn_block)) {
                blockUser()
                ToastUtils.showToast("Block successfully")
            }
        }

        // 添加举报选项
        sheet.addAction(getString(R.string.str_btn_report)) {
            showReportDialog()
        }

        // 显示对话框
        sheet.build().show(supportFragmentManager, "BroadcasterMoreOptionsSheet")
    }

    private fun followUser() {
        if (viewModel.userInfo != null && viewModel.userInfo?.isFriend == false) {
            binding.followLayout.addFriend(lifecycleScope, viewModel.userInfo?.userId!!) { friend ->
                if (friend) {
                    viewModel.userInfo?.isFriend = true
                    ToastUtils.showToast("Follow successfully")
                } else {
                    ToastUtils.showToast("Follow failed")
                }
            }
        }
    }

    private fun unfollowUser() {
        val userId = viewModel.userInfo?.userId ?: return
        binding.followLayout.isEnabled = false
        viewModel.unfollow(userId) { success ->
            runOnUiThread {
                binding.followLayout.isEnabled = true
                if (success) {
                    viewModel.userInfo?.isFriend = false
                    binding.followLayout.setIsFriend(false)
                    ToastUtils.showToast("Unfollow successfully")
                } else {
                    ToastUtils.showToast("Unfollow failed")
                }
            }
        }
    }

    private fun blockUser() {
        val userId = viewModel.userInfo?.userId ?: return
        viewModel.block(userId) { success ->
            runOnUiThread {
                if (success) {
                    ToastUtils.showToast("Block successfully")
                    // TODO: 全局屏蔽该主播相关内容（墙、消息、匹配、电话等）
                } else {
                    ToastUtils.showToast("Block failed")
                }
            }
        }
    }

    private fun unblockUser() {
        val userId = viewModel.userInfo?.userId ?: return
        viewModel.unblock(userId) { success ->
            runOnUiThread {
                if (success) {
                    ToastUtils.showToast("Unblock successfully")
                    // TODO: 取消全局屏蔽该主播
                } else {
                    ToastUtils.showToast("Unblock failed")
                }
            }
        }
    }

    private fun showReportDialog() {
        val reportOptions = resources.getStringArray(R.array.report_reason)/*val reportOptions = arrayOf(
            "Abusive Behavior", "Cheat Behavior", "Pornography Behavior",
            "Bloody Violence", "Harassment Behavior", "Others"
        )*/

        // 创建举报选项的底部对话框
        val reportSheet = CommonActionBottomSheet.builder()
//            .setTitle("举报原因")

        // 添加所有举报选项
        reportOptions.forEachIndexed { index, option ->
            reportSheet.addAction(option) {
                val userId = viewModel.userInfo?.userId ?: return@addAction
                val complainSub = reportOptions[index]

                viewModel.reportUser(userId, "Report", complainSub) { success ->
                    runOnUiThread {
                        if (success) {
                            ToastUtils.showToast("Report Successfully")
                        } else {
                            ToastUtils.showToast("Report Failed")
                        }
                    }
                }
            }
        }

        reportSheet.build().show(supportFragmentManager, "ReportOptionsSheet")
    }
}