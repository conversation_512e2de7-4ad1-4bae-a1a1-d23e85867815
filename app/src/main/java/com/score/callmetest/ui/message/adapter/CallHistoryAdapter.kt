package com.score.callmetest.ui.message.adapter

import android.content.Context
import android.graphics.Point
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallStatus
import com.score.callmetest.CallType
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemCallHistoryBinding
import com.score.callmetest.databinding.ItemListBottomBinding
import com.score.callmetest.entity.CallHistoryEntity
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.DisplayUtils
import timber.log.Timber


/**
 * 通话历史记录列表适配器
 * 实现消息列表的展示和长按菜单功能
 */
class CallHistoryAdapter(private val lifecycleOwner: LifecycleOwner) : ListAdapter<Any, RecyclerView.ViewHolder>(DiffCallback()) {

    companion object {
        private const val VIEW_TYPE_CALL_HISTORY = 0
        private const val VIEW_TYPE_BOTTOM = 1
    }

    // 实时状态缓存，优先使用这里的状态而不是数据库中的历史状态
    private val realtimeStatusCache = mutableMapOf<String, String>()

    // 记录长按的位置
    private val mTouchPoint: Point = Point()

    // 当前显示的菜单
    private var mPopupWindow: PopupWindow? = null

    // popupview相关
    private val mInflater =
        CallmeApplication.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    private var mPopupView: View? = null

    // 用于计算popupView显示位置
    private var mMenuHight: Int = 0
    private var mMenuWidth: Int = 0
    private val _screenHight: Int = DeviceUtils.getScreenHeight(CallmeApplication.context)
    private val _screenWidth: Int = DeviceUtils.getScreenWidth(CallmeApplication.context)
    private val ANCHORED_GRAVITY = Gravity.TOP or Gravity.START
    private val VERTICAL_OFFSET: Int = DisplayUtils.dp2px(10f)

    // popupView text
    private val _strDelete: String by lazy { CallmeApplication.context.getString(R.string.msg_item_menu_delete) }
    private val _strPin: String by lazy { CallmeApplication.context.getString(R.string.msg_item_menu_pin) }
    private val _strUnpin: String by lazy { CallmeApplication.context.getString(R.string.msg_item_menu_unpin) }
    // 添加隐藏选项文本
    private val _strHiden: String by lazy { CallmeApplication.context.getString(R.string.msg_item_menu_hide) }

    private var onCallItemClickListener: OnCallItemClickListener? = null

    /**
     * 设置通话记录点击监听器
     */
    fun setOnCallItemClickListener(listener: OnCallItemClickListener) {
        this.onCallItemClickListener = listener
    }

    override fun getItemViewType(position: Int): Int {
        return if (getItem(position) is CallHistoryEntity) {
            VIEW_TYPE_CALL_HISTORY
        } else {
            VIEW_TYPE_BOTTOM
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        // popupView inflate
        mPopupView = mInflater.inflate(R.layout.layout_msg_item_menu, null)
        // 计算高宽
        mMenuHight = mPopupView?.measuredHeight ?: 0
        mMenuWidth = mPopupView?.measuredWidth ?: 0

        return when (viewType) {
            VIEW_TYPE_CALL_HISTORY -> {
                val binding = ItemCallHistoryBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                CallHistoryViewHolder(binding)
            }
            VIEW_TYPE_BOTTOM -> {
                val binding = ItemListBottomBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                BottomViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is CallHistoryViewHolder -> {
                val callHistory = getItem(position) as CallHistoryEntity
                holder.bind(callHistory, position)
            }
            is BottomViewHolder -> {
                holder.bind("Bottom")
            }
        }
    }
    /**
     * 更新视频图标的显示状态
     * 默认显示video_follow图标，只有当在线状态为ONLINE时才显示call_video图标
     * 优先使用实时状态缓存，避免显示过期的数据库状态
     */
    private fun updateVideoIndicatorVisibility(videoIndicator: View, userId: String, fallbackStatus: String) {
        if (videoIndicator is android.widget.ImageView) {
            // 始终显示视频图标
            videoIndicator.visibility = View.VISIBLE

            // 优先使用实时状态缓存，如果没有则使用传入的fallback状态
            val actualStatus = realtimeStatusCache[userId] ?: fallbackStatus

            // 根据在线状态设置不同的图标
            if (actualStatus == CallStatus.ONLINE) {
                videoIndicator.setImageResource(R.drawable.call_video)
            } else {
                videoIndicator.setImageResource(R.drawable.video_follow)
            }
        }
    }


    /**
     * 释放资源
     */
    fun release() {
        dismissMenu()
        realtimeStatusCache.clear()
        this.onCallItemClickListener = null
    }

    /**
     * 关闭当前菜单
     */
    fun dismissMenu() {
        mPopupWindow?.dismiss()
        mPopupWindow = null
        Timber.tag("CallHistoryAdapter: ").d("PopupMenu dismissed")
    }

    /**
     * 通话记录ViewHolder
     */
    inner class CallHistoryViewHolder(private val binding: ItemCallHistoryBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(callHistory: CallHistoryEntity, position: Int) {
            binding.apply {
                // 设置头像
                Glide.with(ivAvatar)
                    .load(callHistory.avatar)
                    .placeholder(R.drawable.placeholder)
                    .error(R.drawable.placeholder)
                    .into(ivAvatar)

                // 设置用户名
                tvUsername.text = callHistory.userName

                // 获取显示用的通话类型
                val displayCallType = CallType.getDisplayType(callHistory.callType)

                // 设置通话状态图标
                val callTypeIcon = when (displayCallType) {
                    CallType.INCOMING_CALL -> R.drawable.call_connected
                    CallType.CANCELLED_CALL -> R.drawable.call_disconnected
                    CallType.UNANSWERED_CALL -> R.drawable.call_disconnected
                    CallType.REJECTED_CALL -> R.drawable.call_disconnected
                    CallType.MISSED_CALL -> R.drawable.call_disconnected
                    else -> null
                }

                if (callTypeIcon != null) {
                    ivCallType.setImageResource(callTypeIcon)
                }

                // 设置通话类型和时长
                val callTypeText = if (displayCallType == CallType.INCOMING_CALL && callHistory.callDuration > 0) {
                    // 对于INCOMING_CALL类型，显示时间
                    "$displayCallType ${callHistory.getCallDurationText()}"
                } else {
                    displayCallType
                }
                tvCallType.text = callTypeText

                // 设置视频通话标识
                // 优先使用实时状态缓存，避免显示过期的数据库状态
                updateVideoIndicatorVisibility(ivVideoIndicator, callHistory.userId, callHistory.onlineStatus)

                // 设置时间戳
                tvTimestamp.text = formatCallEndTime(callHistory.callEndTime)
                
                // 设置置顶状态背景
                updateItemBackground(callHistory.isPinned)
                
                // 设置点击和长按事件
                setupClickListeners(callHistory, position)
            }
        }
        
        private fun setupClickListeners(callHistory: CallHistoryEntity, position: Int) {
            // 头像点击
            ClickUtils.setOnGlobalDebounceClickListener(binding.ivAvatar) {
                onCallItemClickListener?.onItemAvatarClick(position, callHistory)
            }

            // 视频通话图标点击
            ClickUtils.setOnGlobalDebounceClickListener(binding.ivVideoIndicator) {
                onCallItemClickListener?.onVideoCallClick(position, callHistory)
            }
            binding.root.apply {
                // 点击事件
                ClickUtils.setOnGlobalDebounceClickListener(this) {
                    onCallItemClickListener?.onItemClick(position, callHistory)

                }

                // 触摸事件，记录长按位置
                setOnTouchListener { _, event ->
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            mTouchPoint.set(event.rawX.toInt(), event.rawY.toInt())
                        }
                    }
                    // 不消费事件，继续传递
                    false
                }

                // 长按事件
                setOnLongClickListener {
                    // 使用ClickUtils防抖，避免频繁弹出菜单
                    if (!ClickUtils.isFastClickGlobal()) {
                        val position = bindingAdapterPosition
                        if (position != RecyclerView.NO_POSITION) {
                            Timber.tag("CallHistoryAdapter: ").d("Call history item long clicked: ${callHistory.userName}")
                            showPowerMenu(context, this, callHistory, position)
                        }
                    } else {
                        Timber.tag("CallHistoryAdapter: ").d("Fast long click blocked: ${callHistory.userName}")
                    }
                    true
                }
            }
        }

        /**
         * 使用PopupWindow显示弹出菜单
         * @param context 上下文
         * @param anchorView 锚点视图
         * @param callHistory 通话记录对象
         * @param position 位置
         */
        private fun showPowerMenu(
            context: Context,
            anchorView: View,
            callHistory: CallHistoryEntity,
            position: Int
        ) {
            // 关闭之前的菜单
            dismissMenu()
            Timber.tag("CallHistoryAdapter: ").d("Showing menu for item: ${callHistory.userName}")

            mPopupView?.apply {
                // 使用独立的点击监听器，不共享防抖时间戳，每个菜单项独立计时
                // hiden
                ClickUtils.setOnIsolatedClickListener(findViewById(R.id.layout_msg_item_menu_hide)) {
                    handleMenuItemClick(_strHiden, callHistory, position)
                }

                // delete
                ClickUtils.setOnIsolatedClickListener(findViewById(R.id.layout_msg_item_menu_delete)) {
                    handleMenuItemClick(_strDelete, callHistory, position)
                }
                
                // pin or unpin
                val pinButton = findViewById<TextView>(R.id.tv_msg_item_menu_pin)
                pinButton.text = if (callHistory.isPinned) _strUnpin else _strPin
                ClickUtils.setOnIsolatedClickListener(findViewById(R.id.layout_msg_item_menu_pin)) {
                    val actionText = pinButton.text.toString()
                    handleMenuItemClick(actionText, callHistory, position)
                }
            }

            // popup设置
            mPopupWindow = PopupWindow(
                mPopupView,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                isOutsideTouchable = true
                isFocusable = true
            }

            // 显示菜单
            if (mTouchPoint.x <= _screenWidth / 2) {
                if (mTouchPoint.y + mMenuHight < _screenHight) {
                    mPopupWindow?.showAtLocation(
                        anchorView,
                        ANCHORED_GRAVITY,
                        mTouchPoint.x,
                        mTouchPoint.y + VERTICAL_OFFSET
                    )
                } else {
                    mPopupWindow?.showAtLocation(
                        anchorView,
                        ANCHORED_GRAVITY,
                        mTouchPoint.x,
                        mTouchPoint.y - mMenuHight - VERTICAL_OFFSET
                    )
                }
            } else {
                if (mTouchPoint.y + mMenuHight < _screenHight) {
                    mPopupWindow?.showAtLocation(
                        anchorView,
                        ANCHORED_GRAVITY,
                        mTouchPoint.x - mMenuWidth,
                        mTouchPoint.y + VERTICAL_OFFSET
                    )
                } else {
                    mPopupWindow?.showAtLocation(
                        anchorView,
                        ANCHORED_GRAVITY,
                        mTouchPoint.x - mMenuWidth,
                        mTouchPoint.y - mMenuHight - VERTICAL_OFFSET
                    )
                }
            }
        }

        /**
         * 处理菜单项点击事件
         */
        private fun handleMenuItemClick(
            title: String,
            callHistory: CallHistoryEntity,
            position: Int
        ) {
            when (title) {
                _strHiden -> onCallItemClickListener?.onHideItem(position, callHistory)
                _strDelete -> onCallItemClickListener?.onDeleteItem(position, callHistory)
                _strPin, _strUnpin -> {
                    val newPinnedState = !callHistory.isPinned
                    // 立即更新UI背景
                    updateItemBackground(newPinnedState)
                    // 更新数据并通知监听器
                    callHistory.isPinned = newPinnedState
                    onCallItemClickListener?.onPinItem(position, callHistory, newPinnedState)
                }
            }
            mPopupWindow?.dismiss()
        }
        
        /**
         * 更新项目背景
         * @param isPinned 是否置顶
         */
        private fun updateItemBackground(isPinned: Boolean) {
            binding.root.setBackgroundResource(
                if (isPinned) R.drawable.bg_message_item_pinned
                else R.drawable.bg_mine_function_item_selector
            )
        }

        /**
         * 格式化通话结束时间
         * 通话结束时间，显示yyyy.MM.dd
         */
        private fun formatCallEndTime(timeInMillis: Long): String {
            return android.text.format.DateFormat.format("yyyy.MM.dd", timeInMillis).toString()
        }
    }

    /**
     * 底部ViewHolder
     */
    inner class BottomViewHolder(private val binding: ItemListBottomBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(text: String) {
            binding.tvBottomText.text = "Bottom"
        }
    }

    /**
     * DiffCallback
     */
    class DiffCallback : DiffUtil.ItemCallback<Any>() {
        override fun areItemsTheSame(oldItem: Any, newItem: Any): Boolean {
            return if (oldItem is CallHistoryEntity && newItem is CallHistoryEntity) {
                oldItem.userId == newItem.userId && oldItem.callEndTime == newItem.callEndTime
            } else {
                oldItem == newItem
            }
        }

        override fun areContentsTheSame(oldItem: Any, newItem: Any): Boolean {
            return if (oldItem is CallHistoryEntity && newItem is CallHistoryEntity) {
                oldItem == newItem
            } else {
                oldItem == newItem
            }
        }
    }

    /**
     * 通话记录点击监听器
     */
    interface OnCallItemClickListener {
        fun onItemClick(position: Int, callHistory: CallHistoryEntity)
        fun onHideItem(position: Int, callHistory: CallHistoryEntity)
        fun onDeleteItem(position: Int, callHistory: CallHistoryEntity)
        fun onItemAvatarClick(position: Int, callHistory: CallHistoryEntity)
        fun onPinItem(position: Int, callHistory: CallHistoryEntity, isPinned: Boolean)
        fun onVideoCallClick(position: Int, callHistory: CallHistoryEntity)
    }
}