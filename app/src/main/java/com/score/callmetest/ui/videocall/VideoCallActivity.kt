package com.score.callmetest.ui.videocall

import android.content.Context
import android.content.Intent
import android.view.KeyEvent
import android.view.WindowManager
import androidx.fragment.app.Fragment
import com.score.callmetest.Constant
import com.score.callmetest.databinding.ActivityVideoCallBinding
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.LogReportManager
import com.score.callmetest.manager.OnCallMessage
import com.score.callmetest.manager.ScreenCaptureManager
import com.score.callmetest.manager.VideoCallState
import com.score.callmetest.network.LiveCallAction
import com.score.callmetest.network.LiveCallExt
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.videocall.ongoing.CallOngoingFragment
import com.score.callmetest.util.AgodaUtils
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonPrimitive

class VideoCallActivity : BaseActivity<ActivityVideoCallBinding, VideoCallViewModel>() {

    /**
     * 获取VideoCallViewModel实例
     * 供Fragment使用
     */
    fun getVideoCallViewModel(): VideoCallViewModel {
        return viewModel
    }

    companion object {
        private const val EXTRA_CALL_STATE = "extra_call_state"

        fun startOutgoing(
            context: Context,
            userId: String,
            avatarUrl: String,
            nickname: String,
            age: String,
            country: String,
            unitPrice: String,
        ) {
            if (context is VideoCallActivity) {
                context.showFragment(
                    CallOutgoingFragment(
                        userId = userId,
                        avatarUrl = avatarUrl,
                        nickname = nickname,
                        age = age,
                        country = country,
                        unitPrice = unitPrice
                    )
                )
                return
            }

            val intent = Intent(context, VideoCallActivity::class.java)
            intent.putExtra(Constant.EXTRA_CALL_STATE, VideoCallState.OUTGOING.name)
            intent.putExtra(Constant.USER_ID, userId)
            intent.putExtra(Constant.AVATAR_URL, avatarUrl)
            intent.putExtra(Constant.NICKNAME, nickname)
            intent.putExtra(Constant.AGE, age)
            intent.putExtra(Constant.COUNTRY, country)
            intent.putExtra(Constant.UNIT_PRICE, unitPrice)

            if (context !is android.app.Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        }

        fun startIncoming(
            context: Context,
            callInfo: OnCallMessage
        ) {
            if (context is VideoCallActivity) {
                context.showFragment(
                    CallIncomingFragment(
                        channelName = callInfo.channelName!!,
                        userId = callInfo.fromUserId!!,
                        avatarUrl = callInfo.avatarThumbUrl ?: callInfo.avatar,
                        nickname = callInfo.nickname,
                        age = callInfo.age?.toString(),
                        country = callInfo.country,
                        freeTip = callInfo.uiTips,
                    )
                )
                return
            }

            val intent = Intent(context, VideoCallActivity::class.java)
            intent.putExtra(Constant.EXTRA_CALL_STATE, VideoCallState.INCOMING.name)
            intent.putExtra(Constant.USER_ID, callInfo.fromUserId)
            intent.putExtra(Constant.CHANNEL_NAME, callInfo.channelName)
            intent.putExtra(Constant.AVATAR_URL, callInfo.avatarThumbUrl ?: callInfo.avatar)
            intent.putExtra(Constant.NICKNAME, callInfo.nickname)
            intent.putExtra(Constant.AGE, callInfo.age?.toString())
            intent.putExtra(Constant.COUNTRY, callInfo.country)
            intent.putExtra(Constant.FREE_TIP, callInfo.uiTips)

            // 只有非Activity context时才加FLAG_ACTIVITY_NEW_TASK | CLEAR_TOP | SINGLE_TOP
            if (context !is android.app.Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            } else {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            }
            context.startActivity(intent)
        }

        fun startOngoing(
            context: Context,
            channelName: String?,
            fromUserId: String?,
            toUserId: String?
        ) {
            if (context is VideoCallActivity) {
                context.showFragment(
                    CallOngoingFragment(
                        channelName = channelName,
                        fromUserId = fromUserId,
                        toUserId = toUserId,
                    )
                )
                return
            }

            val intent = Intent(context, VideoCallActivity::class.java)
            intent.putExtra(Constant.EXTRA_CALL_STATE, VideoCallState.ONGOING.name)
            intent.putExtra(Constant.USER_ID, fromUserId)
            intent.putExtra(Constant.CHANNEL_NAME, channelName)
            intent.putExtra(Constant.FROM_USER_ID, fromUserId)
            intent.putExtra(Constant.TO_USER_ID, toUserId)
            // 只有非Activity context时才加FLAG_ACTIVITY_NEW_TASK | CLEAR_TOP | SINGLE_TOP
            if (context !is android.app.Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            } else {
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            }
            context.startActivity(intent)
        }
    }

    override fun getViewModelClass() = VideoCallViewModel::class.java
    override fun getViewBinding() = ActivityVideoCallBinding.inflate(layoutInflater)

    private fun showFragment(fragment: Fragment) {
        supportFragmentManager.beginTransaction()
            .replace(binding.fragmentContainer.id, fragment)
            .commitAllowingStateLoss()
    }

    var state = VideoCallState.OUTGOING

    override fun initView() {
        setKeepScreenOn()
        ScreenCaptureManager.setSecureFlags(this)

        if (!AgodaUtils.isInitialized()) {
            val appId =
                AppConfigManager.getDecryptedAppConfig()?.items?.find { it.name == "rtck" }?.data?.jsonPrimitive?.contentOrNull
            AgodaUtils.initializeGlobal(this, appId)
        }
        val stateName = intent.getStringExtra(Constant.EXTRA_CALL_STATE)
        state = try {
            VideoCallState.valueOf(stateName ?: VideoCallState.OUTGOING.name)
        } catch (e: Exception) {
            VideoCallState.OUTGOING
        }
        viewModel.userId = intent.getStringExtra(Constant.USER_ID)
        if (viewModel.userId.isNullOrEmpty()) {
            finish()
            return
        }

        // 设置用户信息到ViewModel
        viewModel.setUserInfo(
            userId = viewModel.userId!!,
            nickname = intent.getStringExtra(Constant.NICKNAME),
            avatarUrl = intent.getStringExtra(Constant.AVATAR_URL)
        )
        // 检查摄像头和麦克风权限
        AppPermissionManager.checkAndRequestCameraMicrophonePermission(
            this,
            onGranted = {
                LogReportManager.reportLiveCallEvent(
                    action = LiveCallAction.PERMISSION_RESULT,
                    ext = LiveCallExt.PRESS,
                )

                // 权限授权成功，显示对应的Fragment
                when (state) {
                    VideoCallState.OUTGOING -> {
                        showFragment(
                            CallOutgoingFragment(
                                userId = viewModel.userId!!,
                                avatarUrl = intent.getStringExtra(Constant.AVATAR_URL)!!,
                                nickname = intent.getStringExtra(Constant.NICKNAME)!!,
                                age = intent.getStringExtra(Constant.AGE)!!,
                                country = intent.getStringExtra(Constant.COUNTRY)!!,
                                unitPrice = intent.getStringExtra(Constant.UNIT_PRICE)!!,
                            )
                        )
                    }

                    VideoCallState.INCOMING -> showFragment(
                        CallIncomingFragment(
                            userId = viewModel.userId!!,
                            avatarUrl = intent.getStringExtra(Constant.AVATAR_URL),
                            channelName = intent.getStringExtra(Constant.CHANNEL_NAME).toString(),
                            nickname = intent.getStringExtra(Constant.NICKNAME),
                            age = intent.getStringExtra(Constant.AGE),
                            country = intent.getStringExtra(Constant.COUNTRY),
                            freeTip = intent.getStringExtra(Constant.FREE_TIP),
                        )
                    )

                    VideoCallState.ONGOING -> showFragment(
                        CallOngoingFragment(
                            intent.getStringExtra(Constant.CHANNEL_NAME),
                            intent.getStringExtra(Constant.FROM_USER_ID),
                            intent.getStringExtra(Constant.TO_USER_ID),
                        )
                    )

                    else -> {

                    }
                }
            },
            onDenied = {
                LogReportManager.reportLiveCallEvent(
                    action = LiveCallAction.PERMISSION_RESULT,
                    ext = LiveCallExt.REFUSE,
                )
                // 权限被拒绝，结束Activity
                finish()
            }
        )
    }

    override fun initListener() {
        DualChannelEventManager.observeOnPickUp(this) { pickUpMsg ->
            // 这里是对方接听后回调。
            // 断点发现pickUpMsg.fromUserId是对方ID，pickUpMsg.toUserId是自己ID
            // 所以这里要更换位置

            // 对方接听成功，标记通话开始时间
            viewModel.markCallStart()
            viewModel.hasEnteredOngoing = true

            VideoCallActivity.startOngoing(
                this@VideoCallActivity,
                pickUpMsg.channelName,
                pickUpMsg.toUserId.toString(),
                pickUpMsg.fromUserId.toString()
            )
        }
    }

    override fun initData() {

    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        AppPermissionManager.handleRequestPermissionsResult(
            this,
            requestCode,
            permissions,
            grantResults
        )
    }

    /**
     * 设置防息屏
     */
    private fun setKeepScreenOn() {
        window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    /**
     * 取消防息屏
     */
    private fun clearKeepScreenOn() {
        window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onDestroy() {
        AgodaUtils.leaveChannel()
        AgodaUtils.destroy()
        clearKeepScreenOn()
        super.onDestroy()
    }

    fun getUserId(): String? {
        return viewModel.userId
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // 拦截回退
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

}