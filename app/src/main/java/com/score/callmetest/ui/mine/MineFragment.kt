package com.score.callmetest.ui.mine

import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentMineBinding
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoEvent
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.main.CoinStoreActivity
import com.score.callmetest.ui.mine.follow.FollowActivity
import com.score.callmetest.ui.preview.MultiImagePreviewActivity
import com.score.callmetest.ui.profile.ProfileActivity
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.LanguageUtils
import com.score.callmetest.util.click

class MineFragment : BaseFragment<FragmentMineBinding, MineViewModel>() {

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentMineBinding {
        return FragmentMineBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = MineViewModel::class.java

    override fun initView() {
        binding.coinCard.background = DrawableUtils.createRoundRectDrawable(
            ContextCompat.getColor(requireContext(), R.color.bg_coin_card_normal),
            DisplayUtils.dp2pxInternalFloat(14f)
        )
        binding.lineFunction.background = DrawableUtils.createRoundRectDrawable(
            Color.WHITE,
            DisplayUtils.dp2pxInternalFloat(14f)
        )

        if (StrategyManager.isReviewPkg()) {
            binding.customerService.visibility = View.GONE
//            binding.coinCard.visibility = View.GONE
        }
    }

    override fun initData() {
        super.initData()
        // 直接从UserInfo获取数量，初始化显示
        val followingCount = UserInfoManager.myUserInfo?.followingNum ?: 0
        val followersCount = UserInfoManager.myUserInfo?.followNum ?: 0
        binding.tvFollowing.text = followingCount.toString()
        binding.tvFollowers.text = followersCount.toString()

        // 监听用户信息变化事件，直接从UserInfo获取数量
        EventBus.observe(this, UserInfoEvent::class.java) { event ->
            event.userInfo?.let { userInfo ->
                val followingCount = userInfo.followingNum ?: 0
                val followersCount = userInfo.followNum ?: 0

                binding.tvFollowing.text = followingCount.toString()
                binding.tvFollowers.text = followersCount.toString()
            }
        }

        // 监听金币余额
        DualChannelEventManager.observeAvailableCoins(viewLifecycleOwner) { availableCoinsMessage ->
            // 更新余额
            val availableCoins = availableCoinsMessage.coins ?: 0
            UserInfoManager.myUserInfo?.availableCoins = availableCoins
            binding.tvCoinValue.text = availableCoins.toString()
        }



    }

    override fun initListener() {
        // 这里只做示例，实际应遍历include的item_mine_function并设置icon/title/subtitle
        binding.language.click {
            val sheet = LanguageSelectBottomSheet { langCode ->
                LanguageUtils.setAppLanguage(requireContext().applicationContext, langCode)
                // 可选：重启Activity以立即生效
                activity?.recreate()
            }
            sheet.show(parentFragmentManager, "LanguageSelectBottomSheet")
        }

        binding.btnEdit.click {
            ActivityUtils.startActivity(
                <EMAIL>(),
                ProfileActivity::class.java
            )
        }

        // 关注列表点击事件
        binding.followingCard.click {
            val bundle = android.os.Bundle().apply {
                putInt("initial_tab", 0) // 0表示关注页面
            }
            ActivityUtils.startActivity(
                requireContext(),
                FollowActivity::class.java,
                bundle
            )
        }

        // 粉丝列表点击事件
        binding.followerCard.click {
            val bundle = android.os.Bundle().apply {
                putInt("initial_tab", 1) // 1表示粉丝页面
            }
            ActivityUtils.startActivity(
                requireContext(),
                FollowActivity::class.java,
                bundle
            )
        }

        //设置入口
        binding.settings.click {
            ActivityUtils.startActivity(requireContext(), SettingsActivity::class.java)
        }

        binding.customerService.click {
            // 先查缓存，再网络查询
            UserInfoManager.getUserInfo(StrategyManager.strategyConfig?.userServiceAccountId){ getUserInfo ->
                getUserInfo?.let { nonNullUser ->
                    ChatActivity.start(context, nonNullUser)
                }
            }
        }

        // 头像点击预览
        binding.ivAvatar.click {
            val avatarUrl = UserInfoManager.myUserInfo?.avatarUrl
            if (!avatarUrl.isNullOrEmpty()) {
                val intent = Intent(requireContext(), MultiImagePreviewActivity::class.java)
                val uri = avatarUrl.toUri()
                val uriList = arrayListOf(uri)
                intent.putParcelableArrayListExtra("imageUris", uriList)
                intent.putExtra("startIndex", 0)
                startActivity(intent)
            }
        }

        binding.coinCard.click {
            ActivityUtils.startActivity(requireContext(), CoinStoreActivity::class.java)
        }
        EventBus.observe(this, UserInfoEvent::class.java) { event ->
            if (event.userInfo!!.userId == UserInfoManager.myUserInfo!!.userId) {
                refreshInfo()
            }
        }
    }

    fun refreshInfo() {
        if (UserInfoManager.myUserInfo == null) {
            return
        }
        UserInfoManager.refreshMyUserInfo()
        GlideUtils.load(
            context = <EMAIL>(),
            imageView = binding.ivAvatar,
            placeholder = R.drawable.placeholder,
            url = UserInfoManager.myUserInfo?.avatarUrl
        )

        // 顶部信息
        binding.tvNickname.text = UserInfoManager.myUserInfo!!.nickname
        binding.tvNickname.setTypeface(Typeface.create("sans-serif", Typeface.BOLD))

        // 初始化关注和粉丝数量
        binding.tvFollowing.text = "0"
        binding.tvFollowers.text = "0"

        // 初始化金币显示
        binding.tvCoinValue.text = (UserInfoManager.myUserInfo?.availableCoins ?: 0).toString()

        binding.language.setTip(LanguageUtils.getSystemLanguage())
    }

    override fun onResume() {
        super.onResume()
        refreshInfo()
    }

}