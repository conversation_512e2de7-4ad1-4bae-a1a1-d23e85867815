package com.score.callmetest.ui.main

import android.text.Spannable
import android.text.SpannableString
import android.text.style.ImageSpan
import android.view.View
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityCoinStoreBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.CountdownManager
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.PaymentMethodManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.main.RechargeOptionAdapter.Companion.createWithoutLimit
import com.score.callmetest.ui.widget.PaymentMethodPopupWindow
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.launch

/**
 * 金币充值页面
 */
class CoinStoreActivity : BaseActivity<ActivityCoinStoreBinding, CoinStoreViewModel>() {
    private var adapter: RechargeOptionAdapter? = null

    override fun getViewBinding() = ActivityCoinStoreBinding.inflate(layoutInflater)
    override fun getViewModelClass() = CoinStoreViewModel::class.java

    override fun initView() {
        binding.tvTitle.text = getString(R.string.coinStore)
        // 返回按钮点击关闭页面
        binding.ivBack.click {
            finish()
        }
        binding.tvMyCoins.text = myCoins()
        binding.rvRechargeOptions.layoutManager = GridLayoutManager(this, 2)
        // 添加卡片间距，13dp
        val spacing = DisplayUtils.dp2px(13f)
        binding.rvRechargeOptions.addItemDecoration(GridSpacingItemDecoration(2, spacing, true))

        binding.btnCustomerService.click { /* TODO: 跳转客服页面 */ }

        // 设置支付方式选择入口
        setupPaymentMethodSelector()

    }

    override fun initData() {
        super.initData()
        updateGoodsUI()
    }

    private fun showPaymentMethodPopup() {
        // 弹窗显示前，更改箭头方向并隐藏折扣标签
        binding.ivPaymentArrow.setImageResource(R.drawable.btn_up_arrow)
        binding.linePaymentDiscount.visibility = View.GONE

        val popup = PaymentMethodPopupWindow(
            context = this,
            anchorView = binding.paymentMethodSelector,
            onPaymentMethodSelected = { payChannel ->
                // 隐藏红点
                PaymentMethodManager.hidePaymentMethodRedDot()

                // 保存用户选择的支付方式
                PaymentMethodManager.saveLastUsedPaymentMethod(payChannel)

                //todo 用户选择的支付方式
                Toast.makeText(this, "Payment method selected: $payChannel", Toast.LENGTH_SHORT)
                    .show()
            }
        )

        // 先调用show()方法初始化popupWindow
        popup.show()

        // 监听弹窗消失事件，恢复箭头方向
        popup.setOnDismissListener {
            binding.ivPaymentArrow.setImageResource(R.drawable.btn_down_arrow)
            setupPaymentMethodSelector()
        }
    }

    private fun setupPaymentMethodSelector() {
        if (PaymentMethodManager.shouldShowPaymentMethodSelector()) {
            binding.paymentMethodSelector.visibility = View.VISIBLE

            // 检查是否需要显示红点
            if (PaymentMethodManager.shouldShowPaymentMethodRedDot()) {
                binding.paymentMethodRedDot.visibility = View.VISIBLE
            } else {
                binding.paymentMethodRedDot.visibility = View.GONE
            }


            // 显示当前选择的支付方式
            val currentPaymentMethod = PaymentMethodManager.getDefaultPaymentMethod()
            if (currentPaymentMethod != null) {
                binding.tvPaymentMethod.text =
                    PaymentMethodManager.getPaymentChannelDisplayName(currentPaymentMethod)

                // 如果有折扣，显示折扣信息
                val discount = PaymentMethodManager.getChannelDiscount(currentPaymentMethod)
                if (discount > 0) {
                    binding.linePaymentDiscount.visibility = View.VISIBLE
                    binding.tvPaymentBonus.text = "+$discount%"
                } else {
                    binding.linePaymentDiscount.visibility = View.GONE
                }
            }

            binding.paymentMethodSelector.click {
                showPaymentMethodPopup()
            }
        } else {
            binding.linePaymentDiscount.visibility = View.GONE
            binding.paymentMethodSelector.visibility = View.GONE
        }
    }

    //在我的金币中插入金币的图标
    private fun myCoins(): SpannableString {
        // 用图片插入到'My coins: '和数字之间
        val coinCount = UserInfoManager.myUserInfo?.availableCoins ?: 0
        val text = "My coins:   $coinCount" // 注意: 后面有两个空格，方便插入图片
        val spannable = SpannableString(text)
        val drawable = ContextCompat.getDrawable(this, R.drawable.coin)
        drawable?.setBounds(0, 0, DisplayUtils.dp2px(18f), DisplayUtils.dp2px(18f))
        val imageSpan = drawable?.let { ImageSpan(it, ImageSpan.ALIGN_BOTTOM) }
        if (imageSpan != null) {
            val insertPos = text.indexOf("  ") + 1
            spannable.setSpan(
                imageSpan,
                insertPos,
                insertPos + 1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        return spannable
    }


    private fun updateGoodsUI() {
        val cachedGoods = GoodsManager.getCachedAllGoods()
        if (cachedGoods.isNotEmpty()) {
            updateGoodsListToUI(cachedGoods)
        } else {
            lifecycleScope.launch {
                try {
                    val goodsList = GoodsManager.getAllGoods()
                    updateGoodsListToUI(goodsList)
                } catch (e: Exception) {
                    Toast.makeText(
                        this@CoinStoreActivity,
                        e.message ?: "加载失败",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }
    }

    private fun updateGoodsListToUI(goodsList: List<GoodsInfo>) {
        // 先初始化倒计时状态（使用商品促销类型）
        goodsList.forEach { goods ->
            if (goods.type == "1" && !goods.code.isNullOrEmpty()) {
                val remainTime = goods.remainMilliseconds ?: goods.surplusMillisecond
                if (remainTime != null && remainTime > 0) {
                    CountdownManager.initOrUpdateCountdown(
                        CountdownManager.ActivityType.GOODS_PROMOTION,
                        goods.code,
                        remainTime
                    )
                }
            }
        }

        val rechargeOptions = goodsList
            .sortedWith(compareByDescending<GoodsInfo> { it.isPromotion == true }.thenBy {
                it.price ?: Double.MAX_VALUE
            })
            .map { goods ->
                RechargeOption(
                    iconRes = GoodsManager.getIconByGoodsId(goods.code),
                    coinAmount = (goods.exchangeCoin ?: 0) + (goods.extraCoin ?: 0),
                    price = "$${goods.price ?: 0.0}",
                    oldPrice = goods.originalPrice?.let { "$${it}" },
                    name = goods.name,
                    tags = goods.tags,
                    extraCoinPercent = goods.extraCoinPercent,
                    bonus = if ((goods.extraCoinPercent
                            ?: 0) > 0
                    ) "+${goods.extraCoinPercent}%" else null,
                    goodsCode = goods.code,
                    type = goods.type,
                    remainMilliseconds = goods.remainMilliseconds ?: goods.surplusMillisecond
                )
            }
        val distinctOptions = rechargeOptions
            .distinctBy { it.goodsCode }
            // 保留了类型为 "0" 的商品，或者类型为 "1" 且剩余时间大于 0 的商品。
            .filter { it.type == "0" || ((it.remainMilliseconds ?: 0L) > 0L) }
        adapter = createWithoutLimit(
            options = distinctOptions,
            onClick = { option -> handleRechargeOptionClick(option) },
            onCountdownEnd = { updateGoodsUI() } // 添加计时结束回调
        )
        binding.rvRechargeOptions.adapter = adapter
    }

    private fun handleRechargeOptionClick(option: RechargeOption) {
        option.goodsCode?.let { goodsCode ->
            lifecycleScope.launch {
                RechargeManager.startRecharge(
                    activity = this@CoinStoreActivity,
                    goodsCode = goodsCode,
                    entry = RechargeSource.SUBSCRIBE_DETAIL
                )
            }
        }
    }

    private fun isNativePay(channel: com.score.callmetest.network.PayChannelItem): Boolean {
        return channel.payChannel == PaymentMethodManager.PAY_CHANNEL_GP
    }
} 