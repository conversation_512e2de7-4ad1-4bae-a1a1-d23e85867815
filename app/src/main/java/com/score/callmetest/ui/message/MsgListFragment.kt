package com.score.callmetest.ui.message

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentMsgListBinding
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.entity.MessageListEntity
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.message.adapter.MsgListAdapter
import com.score.callmetest.ui.widget.BaseCustomDialog
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ToastUtils
import timber.log.Timber

/**
 * Message模块--msg/call列表
 * 展示消息列表，并处理长按菜单功能
 */
class MsgListFragment : BaseFragment<FragmentMsgListBinding, MsgListViewModel>(), IMessageStatus {

    // recyclerview 相关
    private lateinit var mAdapter: MsgListAdapter
    private val mLayoutManager by lazy { LinearLayoutManager(context) }
    
    // 是否需要滚动到顶部的标志
    private var mNeedScrollToTop = false

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentMsgListBinding = FragmentMsgListBinding.inflate(inflater, container, false)

    override fun getViewModelClass(): Class<MsgListViewModel> = MsgListViewModel::class.java

    override fun initView() {
        super.initView()
        setupRecyclerView()
        setupSwipeRefresh()

    }
    
    private fun setupRecyclerView() {
        mAdapter = MsgListAdapter()
        binding.recyclerView.apply {
            adapter = mAdapter
            layoutManager = mLayoutManager
        }
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            // 设置下拉刷新的颜色
            // 暂时不加
            /*setColorSchemeResources(
                android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light
            )*/
            
            // 下拉刷新
            setOnRefreshListener {
                // 显示刷新动画
                isRefreshing = true
                // 设置需要滚动到顶部的标志
                mNeedScrollToTop = true
                // 重新加载数据
                viewModel.loadMessageList()
            }
        }
    }

    override fun initData() {
        super.initData()
        setupObservers()
        binding.emptyView.inflate()
    }
    
    private fun setupObservers() {
        // 通过这个来确保OnResume的准确性
        EventBus.observe(this, CustomEvents.BottomTabSelected::class.java){ tab ->
            if (tab.index == 1) {
                // 更新底部项显示状态
                updateBottomItemVisibility()
            }
        }

        // 观察数据变化
        viewModel.mMessageList.observe(viewLifecycleOwner) { messageList ->
            // empty-view
            if(messageList.isNullOrEmpty()){
                emptyView(true)
                binding.swipeRefreshLayout.isRefreshing = false
                return@observe
            }
            emptyView(false)
            // 提交新列表前保存当前滚动位置
            val firstVisiblePosition = mLayoutManager.findFirstVisibleItemPosition()
            
            mAdapter.submitList(messageList) {
                // 数据加载完成后，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false
                
                // 只有在需要滚动到顶部时才滚动（下拉刷新时）
                if (mNeedScrollToTop) {
                    binding.recyclerView.scrollToPosition(0)
                    mNeedScrollToTop = false
                } else if (firstVisiblePosition == 0) {
                    // 如果原本就在顶部，确保仍然在顶部
                    binding.recyclerView.scrollToPosition(0)
                }
                
                // 列表更新后检查底部项显示状态
                updateBottomItemVisibility()
            }
        }
        
        // 观察加载状态
        viewModel.mIsLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.swipeRefreshLayout.isRefreshing = isLoading
            if (isLoading) {
                mNeedScrollToTop = true
            }
        }
        
        // 观察错误信息
        viewModel.mErrorMessage.observe(viewLifecycleOwner) { errorMessage ->
            if (!errorMessage.isNullOrEmpty()) {
                ToastUtils.showShortToast(errorMessage)
                Timber.tag("dsc--").e("Error loading message list: $errorMessage")
                // 错误发生时，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false
            }
            emptyView(viewModel.mMessageList.value.isNullOrEmpty())
        }

        // 点击调整chat
        viewModel.mGoChatUser.observe(viewLifecycleOwner) { user ->
            ChatActivity.start(context,user)
        }
    }

    /**
     * shou/hide EmptyView
     */
    private fun emptyView(isShow: Boolean){
        if (isShow) {
            // 确保ViewStub已经被inflate
            if (binding.emptyView.parent != null) { // 检查是否还未inflate
                binding.emptyView.inflate()
            }

            // 现在可以安全地访问inflated的视图
            val emptyLayout = binding.root.findViewById<View>(R.id.layout_empty_rv_parent)
            val hintTv = emptyLayout.findViewById<TextView>(R.id.layout_empty_rv_bg_hint_tv)
            hintTv.text = getString(R.string.null_call_history)
            binding.emptyView.visibility = View.VISIBLE
            binding.recyclerView.visibility = View.GONE
        } else {
            binding.emptyView.visibility = View.GONE
            binding.recyclerView.visibility = View.VISIBLE
        }
    }

    override fun initListener() {
        super.initListener()
        binding.recyclerView.apply {
            // 设置滚动监听器
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 只有在顶部时才能下拉刷新
                    val firstVisibleItemPosition = mLayoutManager.findFirstCompletelyVisibleItemPosition()
                    binding.swipeRefreshLayout.isEnabled = firstVisibleItemPosition <= 0
                }
            })
        }
        setupMessageItemListener()
    }

    // <editor-fold desc="IMessageStatus">

    override fun provideUserIds(): List<String> {
        val userIds: List<String> = viewModel.mMessageList.value?.map {
            return@map it.userId
        } ?: emptyList()
        return userIds
    }

    override fun notifyStatusChanged(statusMap: Map<String, String>) {
        viewModel.updateStatus(statusMap)
    }

    // </editor-fold>

    /**
     * 更新底部项显示状态
     */
    private fun updateBottomItemVisibility() {
        // 当列表填充完成后执行
        binding.recyclerView.post {
            // 获取可见项数量
            val visibleItemCount = mLayoutManager.childCount
            // 获取列表总项数
            val totalItemCount = mLayoutManager.itemCount
            
            // 通知 ViewModel 更新底部项显示状态
            viewModel.calculateBottomItemVisibility(totalItemCount, visibleItemCount)
        }
    }

    /**
     * Item相关点击、长按事件
     */
    private fun setupMessageItemListener() {
        // 设置消息项点击监听器
        mAdapter.setOnMessageItemClickListener(object : MsgListAdapter.OnMessageItemClickListener {

            override fun onItemAvatarClick(position: Int, messageObj: MessageListEntity) {
                // 头像点击
                handleItemAvatarClick(messageObj)
            }

            override fun onItemClick(position: Int, messageObj: MessageListEntity) {
                // 点击
                handleItemClick(messageObj)
            }
            
            override fun onHideItem(position: Int, messageObj: MessageListEntity) {
                // 隐藏
                handleHideItem(messageObj)
            }
            
            override fun onDeleteItem(position: Int, messageObj: MessageListEntity) {
                // 删除
                handleDeleteItem(messageObj)
            }
            
            override fun onPinItem(position: Int, messageObj: MessageListEntity, isPinned: Boolean) {
                // 置顶
                handlePinItem(messageObj, isPinned)
            }
        })
    }

    private fun handleItemAvatarClick(user: MessageListEntity){
        // 处理消息项点击事件
        Timber.tag("dsc--").d("头像点击：${user.userName}")

        // 跳转到主播资料详情页面
        val intent = Intent(activity, BroadcasterDetailActivity::class.java)
        intent.putExtra("broadcaster_model",user.toBroadcasterModel())
        startActivity(intent)
    }

    private fun handleItemClick(user: MessageListEntity) {
        // 处理消息项点击事件
        Timber.tag("dsc--").d("Message item clicked: ${user.userName}")
        //  查询userInfo后跳转
        viewModel.gotoChat(user.userId)
    }
    
    private fun handleHideItem(messageObj: MessageListEntity) {
        // 处理隐藏消息事件
        Timber.tag("dsc--").d("Message item hidden: ${messageObj.userName}")
        ToastUtils.showCustomToast("已隐藏: ${messageObj.userName}")
        viewModel.hideMessage(messageObj)
    }
    
    private fun handleDeleteItem(messageObj: MessageListEntity) {
        // 处理删除消息事件
        Timber.tag("dsc--").d("Message item deleted: ${messageObj.userName}")
        ToastUtils.showCustomToast("已删除: ${messageObj.userName}")
        viewModel.deleteMessage(messageObj)
    }
    
    private fun handlePinItem(messageObj: MessageListEntity, isPinned: Boolean) {
        // 处理置顶/取消置顶消息事件
        val action = if (isPinned) "已置顶" else "已取消置顶"
        Timber.tag("dsc--").d("Message item $action: ${messageObj.userName}")
        ToastUtils.showCustomToast("$action: ${messageObj.userName}")
        
        // 更新置顶状态，但不滚动到顶部
        viewModel.pinMessage(messageObj, isPinned)
    }

    /**
     * 一键已读
     */
    fun clearMsg(){
        // 添加防抖功能
        Timber.tag("dsc--").d("一键已读")
        BaseCustomDialog(
            context = requireContext(),
            emojiResId = R.drawable.emoji_msg,
            title = getString(R.string.clean_msg_confirm_title),
            content = getString(R.string.clean_msg_confirm_content),
            onAgree = {
                // 一键已读
                viewModel.clearUnreadMessages()
            },
            onCancel = {}
        ).show()
    }
    
    override fun onResume() {
        super.onResume()
        // 在 onResume 中更新底部项显示状态
        updateBottomItemVisibility()
        updateVisibilityState()
        Timber.tag("MsgListFragment").d("onResume")
    }

    override fun onPause() {
        super.onPause()
        updateVisibilityState()
        Timber.tag("MsgListFragment").d("onPause")
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        updateVisibilityState()
        Timber.tag("MsgListFragment").d("setUserVisibleHint: $isVisibleToUser")
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        updateVisibilityState()
        Timber.tag("MsgListFragment").d("onHiddenChanged: hidden=$hidden")
    }

    /**
     * 更新Fragment可见性状态
     * 只有当Fragment真正可见时才设置为true
     */
    private fun updateVisibilityState() {
        val isVisible = isResumed && userVisibleHint && !isHidden
        MessageIncomingManager.setMsgListFragmentVisible(isVisible)
        Timber.tag("MsgListFragment").d("updateVisibilityState: isVisible=$isVisible (resumed=$isResumed, userVisible=$userVisibleHint, hidden=$isHidden)")
    }

    override fun onDestroyView() {
        // 清除滚动监听器
        binding.recyclerView.clearOnScrollListeners()
        // 释放适配器资源，避免内存泄漏
        mAdapter.release()
        super.onDestroyView()
    }
}