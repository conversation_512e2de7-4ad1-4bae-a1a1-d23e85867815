package com.score.callmetest.ui.main

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.R
import com.score.callmetest.network.GetGiftCountItem

class BroadcasterGiftListAdapter(private val data: List<GetGiftCountItem>) : RecyclerView.Adapter<BroadcasterGiftListAdapter.GiftViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GiftViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_broadcaster_gift, parent, false)
        return GiftViewHolder(view)
    }

    override fun onBindViewHolder(holder: GiftViewHolder, position: Int) {
        val item = data[position]
        Glide.with(holder.ivGift.context)
            .load(item.url)
            .placeholder(R.drawable.gift_placehold)
            .into(holder.ivGift)
        holder.tvGiftCount.text = "x${item.num ?: 0}"
    }

    override fun getItemCount(): Int = data.size

    class GiftViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivGift: ImageView = itemView.findViewById(R.id.iv_gift)
        val tvGiftCount: TextView = itemView.findViewById(R.id.tv_gift_count)
    }
} 