package com.score.callmetest.manager

import android.content.Context
import android.net.Uri
import android.util.Log
import com.score.callmetest.network.OssPolicyResponse
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.ImageUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import org.json.JSONObject
import java.io.File
import java.io.IOException

object OssUploadManager {
    interface UploadCallback {
        fun onSuccess(ossRelativePath: String)
        fun onFailure(error: String)
    }

    private var policyCache: OssPolicyResponse? = null
    private var policyExpireTime: Long = 0L

    fun getOssPolicy(scope: CoroutineScope, forceRefresh: Boolean = false, callback: (OssPolicyResponse?) -> Unit) {
        val now = System.currentTimeMillis() / 1000
        val cached = policyCache
        if (!forceRefresh && cached != null && policyExpireTime > now + 60) {
            callback(cached)
            return
        }
        scope.launch(Dispatchers.IO) {
            try {
                val response = RetrofitUtils.apiService.getOssPolicyPostV2()
                val policy = response.data
                if (policy != null) {
                    policyCache = policy
                    policyExpireTime = policy.expire ?: 0L
                }
                withContext(Dispatchers.Main) {
                    callback(policy)
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) { callback(null) }
            }
        }
    }

    fun compressImage(context: Context, imageUri: Uri): String? {
        return ImageUtils.compressImage(
            context = context,
            imageUri = imageUri,
            quality = 80,
            maxWidth = 1920,
            maxHeight = 1080
        )
    }

    fun getOssKey(dir: String?, fileName: String?): String {
        if (dir.isNullOrEmpty() || fileName.isNullOrEmpty()) return ""
        val suffixIndex = fileName.lastIndexOf(".")
        val fileType = if (suffixIndex > 0) fileName.substring(suffixIndex) else ""
        return "${dir}${System.currentTimeMillis()}${fileType}"
    }

    fun uploadImageToOss(
        policy: OssPolicyResponse,
        localFilePath: String,
        callback: UploadCallback
    ) {
        Log.d("OssUpload", "uploadImageToOss: localFilePath=$localFilePath, policy.host=${policy.host}, dir=${policy.dir}")
        val file = File(localFilePath)
        val requestBody = RequestBody.Companion.create("application/octet-stream".toMediaTypeOrNull(), file)
        val ossKey = getOssKey(policy.dir, file.name)
        Log.d("OssUpload", "ossKey=$ossKey, fileName=${file.name}")
        val body = MultipartBody.Builder().setType(MultipartBody.Companion.FORM)
            .addFormDataPart("OSSAccessKeyId", policy.accessKeyId ?: "")
            .addFormDataPart("policy", policy.policy ?: "")
            .addFormDataPart("signature", policy.signature ?: "")
            .addFormDataPart("key", ossKey)
            .addFormDataPart("callback", policy.callback ?: "")
            .addFormDataPart("file", file.name, requestBody)
            .build()
        val request = Request.Builder()
            .url(policy.host ?: "")
            .post(body)
            .build()
        Log.d("OssUpload", "start upload: url=${policy.host}")
        OkHttpClient().newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("OssUpload", "Upload failed: ${e.message}")
                callback.onFailure(e.message ?: "Upload failed")
            }
            override fun onResponse(call: Call, response: Response) {
                Log.d("OssUpload", "Upload response: code=${response.code}, message=${response.message}")
                if (response.isSuccessful) {
                    val bodyStr = response.body?.string()
                    Log.d("OssUpload", "Upload success, response body: $bodyStr")
                    val filename = parseFilenameFromResponse(bodyStr)
                    callback.onSuccess(filename)
                } else {
                    Log.e("OssUpload", "Upload failed: ${response.message}")
                    callback.onFailure("Upload failed: ${response.message}")
                }
            }
        })
    }

    private fun parseFilenameFromResponse(response: String?): String {
        if (response.isNullOrEmpty()) return ""
        return try {
            val json = JSONObject(response)
            val data = json.optJSONObject("data")
            data?.optString("filename") ?: ""
        } catch (e: Exception) {
            Log.e("OssUpload", "parseFilenameFromResponse error: ${e.message}")
            ""
        }
    }
}