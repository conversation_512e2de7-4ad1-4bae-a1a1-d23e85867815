package com.score.callmetest.manager

import android.view.View
import androidx.annotation.ColorInt
import androidx.annotation.NonNull
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.manager.TranslateManager
import com.score.callmetest.ui.message.MessageIncomingManager
import com.score.callmetest.util.SharePreferenceUtil

object GlobalManager {

    fun getStatusColor(status: String) : Int {
        return when(status) {
            CallStatus.ONLINE, CallStatus.AVAILABLE -> {
                ContextCompat.getColor(CallmeApplication.context,R.color.msg_online)
            }
            CallStatus.OFFLINE -> {
                ContextCompat.getColor(CallmeApplication.context,R.color.msg_offline)
            }
            CallStatus.BUSY, CallStatus.IN_CALL -> {
                ContextCompat.getColor(CallmeApplication.context,R.color.msg_busy)
            }
            else -> {
                ContextCompat.getColor(CallmeApplication.context,R.color.msg_offline)
            }
        }
    }

    fun getMainButtonBgGradientColors() : IntArray {
        return intArrayOf("#FFC720".toColorInt(), "#FF2FBA".toColorInt())
    }

    /**
     * 创建圆角纯色Drawable
     */
    fun setViewRoundBackground(@NonNull view: View, @ColorInt color: Int) {
        view.doOnPreDraw {
            view.background = DrawableUtils.createRoundRectDrawable(color, view.height / 2f)
        }
    }

    fun onLoginSuccess() {
        ThreadUtils.runOnIO {
            SharePreferenceUtil.putBoolean(Constant.KEY_IS_FIRST_LOGIN, false)

            SocketManager.instance.initAndConnect()
            RongCloudCommandManager.instance.initialize()

            // 自动补单
            RechargeManager.checkPendingOrdersWithGooglePlay()

            // 初始化双通道事件管理器
            DualChannelEventManager.initialize()
            GoodsManager.refreshAllGoods()
            GiftManager.loadAllGift()
            PaymentMethodManager.init(StrategyManager.strategyConfig)
            AdjustManager.reportLastAdjustAttribution(CallmeApplication.context, UserInfoManager.myUserInfo?.userId)
            // 登录成功后拉取后置摄像头配置
            RearCameraManager.fetchConfig {}
        }
        TranslateManager.loadCacheAsync()
    }

    /**
     * 用户登出时清理资源
     */
    fun onLogout() {
        // 清理用户信息
        UserInfoManager.clear()
        // 断开Socket连接
        SocketManager.instance.disconnect()
        // 清理融云CommandMessage管理器
        RongCloudCommandManager.instance.destroy()
        // 清理双通道事件管理器
        DualChannelEventManager.cleanup()
        // 清理消息弹窗管理器
        MessageIncomingManager.cleanup()
        // 清理支付信息
        PaymentMethodManager.clear()
        // 清理后置摄像头配置
        RearCameraManager.clear()
        // 声网资源释放
       AgodaUtils.release()

    }

    /**
     * 应用退出时清理资源
     */
    fun onAppExit() {
    }
}