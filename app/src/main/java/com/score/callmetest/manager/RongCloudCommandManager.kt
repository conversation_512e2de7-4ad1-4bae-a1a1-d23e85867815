package com.score.callmetest.manager

import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.lifecycle.LifecycleOwner
import com.google.gson.Gson
import com.score.callmetest.constants.RongCloudConstants
import com.score.callmetest.constants.SocketCommands
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.im.callback.ImOnReceiveMessageListener
import com.score.callmetest.util.EventBus
import io.rong.imlib.model.Message
import io.rong.message.CommandMessage
import org.json.JSONException
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap

/**
 * 融云CommandMessage监听管理器
 * 参照SocketManager的实现方式，监听融云CommandMessage并进行事件分发
 * 
 * 功能特点：
 * 1. 监听三种类型的CommandMessage：MESSAGE_EVENT、RESPONSE_EVENT、ON_GIFT_ASK
 * 2. 使用EventBus进行事件分发，与SocketManager保持一致
 * 3. 线程安全的事件处理
 * 4. 支持生命周期感知的观察者模式
 */
class RongCloudCommandManager private constructor() {
    
    companion object {
        private const val TAG = "RongCloudCommand"
        val instance: RongCloudCommandManager by lazy { RongCloudCommandManager() }
    }
    
    // 主线程Handler，用于确保事件在主线程分发
    private val mainHandler = Handler(Looper.getMainLooper())
    
    // 命令监听器映射，用于处理不同类型的命令
    private val commandMap = ConcurrentHashMap<String, LongLiveListener>()
    
    // 融云消息监听器
    private var rongCloudMessageListener: ImOnReceiveMessageListener? = null
    
    // 是否已初始化
    private var isInitialized = false
    
    /**
     * 长期监听器接口
     * 参照SocketManager的LongLiveListener设计
     */
    interface LongLiveListener {
        fun onReceive(command: String, commandId: String, data: JSONObject?, extra: Any?)
    }
    
    /**
     * 初始化管理器
     * 注册融云消息监听器并设置命令处理器
     */
    fun initialize() {
        if (isInitialized) {
            Log.w(TAG, "RongCloudCommandManager已经初始化")
            return
        }
        
        // 注册融云消息监听器
        rongCloudMessageListener = object : ImOnReceiveMessageListener {
            override fun onReceiveMessage(message: Message) {
                // 子线程
                handleRongCloudMessage(message)
            }
        }
        RongCloudManager.addOnReceiveMessageListener(rongCloudMessageListener!!)
        
        // 设置命令处理器
        setupCommandHandlers()
        
        isInitialized = true
        Log.d(TAG, "RongCloudCommandManager初始化完成")
    }
    
    /**
     * 处理融云CommandMessage
     */
    private fun handleRongCloudMessage(message: Message) {
        // 只处理CommandMessage
        if (message.content !is CommandMessage) {
            return
        }
        
        val commandMessage = message.content as CommandMessage
        val name = commandMessage.name
        val data = commandMessage.data
        
        Log.d(TAG, "收到CommandMessage: name=$name, data=$data")
        
        // 检查是否是我们关心的消息类型
        if (!isSupportedCommand(name)) {
            return
        }

        handleCommandMessage(name, data)
    }
    
    /**
     * 处理CommandMessage数据
     */
    private fun handleCommandMessage(name: String, data: String?) {
        if (data.isNullOrBlank()) {
            Log.w(TAG, "CommandMessage数据为空: name=$name")
            return
        }
        
        try {
            val dataObject = JSONObject(data)
            val code = dataObject.optString("code")
            
            // 检查响应码
            if (code != RongCloudConstants.ResponseCode.SUCCESS) {
                Log.w(TAG, "CommandMessage响应码异常: name=$name, code=$code")
                return
            }
            
            val command = dataObject.optString("command")
            val commandId = dataObject.optString("commandId")
            val commandData = dataObject.optJSONObject("data")
            
            // 根据name类型处理
            when (name) {
                RongCloudConstants.MESSAGE_EVENT -> {
                    // MESSAGE_EVENT类型，使用command字段作为具体命令
                    if (command.isNotBlank()) {
                        val listener = commandMap[command]
                        listener?.onReceive(command, commandId, commandData, null)
                    }
                }
                RongCloudConstants.RESPONSE_EVENT -> {
                    // RESPONSE_EVENT类型，使用command字段作为具体命令
                    if (command.isNotBlank()) {
                        val listener = commandMap[command]
                        listener?.onReceive(command, commandId, commandData, null)
                    }
                }
                RongCloudConstants.ON_GIFT_ASK -> {
                    // ON_GIFT_ASK类型，直接使用name作为命令
                    val listener = commandMap[name]
                    listener?.onReceive(name, commandId, commandData, null)
                }
            }
            
        } catch (e: JSONException) {
            Log.e(TAG, "解析CommandMessage数据失败: name=$name, data=$data", e)
        } catch (e: Exception) {
            Log.e(TAG, "处理CommandMessage失败: name=$name", e)
        }
    }
    
    /**
     * 检查是否是支持的命令类型
     */
    private fun isSupportedCommand(name: String): Boolean {
        return when (name) {
            RongCloudConstants.MESSAGE_EVENT,
            RongCloudConstants.RESPONSE_EVENT,
            RongCloudConstants.ON_GIFT_ASK -> true
            else -> false
        }
    }
    
    /**
     * 设置命令处理器
     * 参照SocketManager的putCommands方法
     */
    private fun setupCommandHandlers() {
        // 获取所有支持的命令
        val supportedCommands = getSupportedCommands()
        
        for (command in supportedCommands) {
            commandMap[command] = object : LongLiveListener {
                override fun onReceive(
                    command: String,
                    commandId: String,
                    data: JSONObject?,
                    extra: Any?
                ) {
                    Log.d(TAG, "处理命令: $command, id: $commandId, data: $data")
                    if (data == null) return
                    
                    try {
                        val gson = Gson()
                        when (command) {
                            // Socket命令相关的处理（RESPONSE_EVENT类型）
                            SocketCommands.Call.ON_CALL -> {
                                val msg = gson.fromJson(data.toString(), OnCallMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("onCall", msg.channelName, msg.fromUserId, msg.toUserId)
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            SocketCommands.Call.ON_HANG_UP -> {
                                val msg = gson.fromJson(data.toString(), OnHangUpMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("onHangUp", msg.channelName, msg.fromUserId?.toString(), msg.toUserId?.toString())
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            SocketCommands.Call.ON_PICK_UP -> {
                                val msg = gson.fromJson(data.toString(), OnPickUpMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("onPickUp", msg.channelName, msg.fromUserId?.toString(), msg.toUserId?.toString())
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            SocketCommands.Call.ESTIMATED_HANG_UP_TIME -> {
                                val msg = gson.fromJson(data.toString(), EstimatedHangUpTimeMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("estimatedHangUpTime", msg.channelName, msg.payUserId, null)
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            SocketCommands.Coins.AVAILABLE_COINS -> {
                                val msg = gson.fromJson(data.toString(), AvailableCoinsMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("availableCoins", null, null, null, msg.coins?.toString())
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            // 消息相关的处理（MESSAGE_EVENT类型）
                            SocketCommands.Message.ON_CHAT -> {
                                val msg = gson.fromJson(data.toString(), OnChatMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("onChat", null, msg.fromUserId, msg.toUserId, msg.timestamp?.toString())
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            // 礼物索要处理（ON_GIFT_ASK类型）
                            RongCloudConstants.ON_GIFT_ASK -> {
                                val msg = gson.fromJson(data.toString(), OnGiftAskMessage::class.java)
                                val eventKey = DualChannelEventManager.generateEventKey("onGiftAsk", null, msg.fromUserId, null, msg.timestamp)
                                if (DualChannelEventManager.shouldProcessEvent(eventKey)) {
                                    EventBus.post(msg)
                                }
                            }
                            else -> {
                                Log.w(TAG, "未处理的命令类型: $command")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "处理命令失败: ${e.message}", e)
                    }
                }
            }
        }
    }
    
    /**
     * 获取支持的命令列表
     */
    private fun getSupportedCommands(): Array<String> {
        return arrayOf(
            // Socket命令（通过RESPONSE_EVENT接收）
            SocketCommands.Call.ON_CALL,
            SocketCommands.Call.ON_HANG_UP,
            SocketCommands.Call.ON_PICK_UP,
            SocketCommands.Call.ESTIMATED_HANG_UP_TIME,
            SocketCommands.Coins.AVAILABLE_COINS,
            // 消息命令（通过MESSAGE_EVENT接收）
            SocketCommands.Message.ON_CHAT,
            // 礼物索要命令（通过ON_GIFT_ASK接收）
            RongCloudConstants.ON_GIFT_ASK
        )
    }
    
    /**
     * 销毁管理器
     * 清理资源和监听器
     */
    fun destroy() {
        if (!isInitialized) {
            return
        }
        
        // 移除融云消息监听器
        rongCloudMessageListener?.let {
            RongCloudManager.removeOnReceiveMessageListener(it)
            rongCloudMessageListener = null
        }
        
        // 清空命令映射
        commandMap.clear()
        
        isInitialized = false
        Log.d(TAG, "RongCloudCommandManager已销毁")
    }

    // 观察者方法，参照SocketManager的实现
    @Deprecated("使用 DualChannelEventManager.observeOnCall 替代", ReplaceWith("DualChannelEventManager.observeOnCall(owner, onEvent)"))
    fun observeOnCall(owner: LifecycleOwner, onEvent: (OnCallMessage) -> Unit) {
        EventBus.observe(owner, OnCallMessage::class.java, onEvent = onEvent)
    }

    @Deprecated("使用 DualChannelEventManager.observeOnHangUp 替代", ReplaceWith("DualChannelEventManager.observeOnHangUp(owner, onEvent)"))
    fun observeOnHangUp(owner: LifecycleOwner, onEvent: (OnHangUpMessage) -> Unit) {
        EventBus.observe(owner, OnHangUpMessage::class.java, onEvent = onEvent)
    }

    @Deprecated("使用 DualChannelEventManager.observeOnPickUp 替代", ReplaceWith("DualChannelEventManager.observeOnPickUp(owner, onEvent)"))
    fun observeOnPickUp(owner: LifecycleOwner, onEvent: (OnPickUpMessage) -> Unit) {
        EventBus.observe(owner, OnPickUpMessage::class.java, onEvent = onEvent)
    }

    @Deprecated("使用 DualChannelEventManager.observeEstimatedHangUpTime 替代", ReplaceWith("DualChannelEventManager.observeEstimatedHangUpTime(owner, onEvent)"))
    fun observeEstimatedHangUpTime(owner: LifecycleOwner, onEvent: (EstimatedHangUpTimeMessage) -> Unit) {
        EventBus.observe(owner, EstimatedHangUpTimeMessage::class.java, onEvent = onEvent)
    }

    @Deprecated("使用 DualChannelEventManager.observeAvailableCoins 替代", ReplaceWith("DualChannelEventManager.observeAvailableCoins(owner, onEvent)"))
    fun observeAvailableCoins(owner: LifecycleOwner, onEvent: (AvailableCoinsMessage) -> Unit) {
        EventBus.observe(owner, AvailableCoinsMessage::class.java, onEvent = onEvent)
    }

    @Deprecated("使用 DualChannelEventManager.observeOnChat 替代", ReplaceWith("DualChannelEventManager.observeOnChat(owner, onEvent)"))
    fun observeOnChat(owner: LifecycleOwner, onEvent: (OnChatMessage) -> Unit) {
        EventBus.observe(owner, OnChatMessage::class.java, onEvent = onEvent)
    }

    @Deprecated("使用 DualChannelEventManager.observeOnGiftAsk 替代", ReplaceWith("DualChannelEventManager.observeOnGiftAsk(owner, onEvent)"))
    fun observeOnGiftAsk(owner: LifecycleOwner, onEvent: (OnGiftAskMessage) -> Unit) {
        EventBus.observe(owner, OnGiftAskMessage::class.java, onEvent = onEvent)
    }
}

/**
 * 礼物索要消息实体
 * 根据RongCloudConstants中的示例数据结构定义
 */
class OnGiftAskMessage(
    val fromUserId: String?,
    val content: String?,
    val timestamp: String?
)
