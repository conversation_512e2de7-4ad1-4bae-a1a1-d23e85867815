package com.score.callmetest.manager

import com.score.callmetest.network.GiftInfo
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

object GiftManager {
    var giftCache: List<GiftInfo>? = null

    suspend fun getGiftList(forceRefresh: Boolean = false): List<GiftInfo> = withContext(Dispatchers.IO) {
        if (!forceRefresh && giftCache != null) return@withContext giftCache!!
        val resp = RetrofitUtils.apiService.getGiftListPostV2()
        val list = (resp.data ?: emptyList()).sortedByDescending { it.sortNo ?: 0 }
        giftCache = list
        list
    }

    fun getGiftList(
        scope: CoroutineScope,
        forceRefresh: Boolean = false,
        onSuccess: (List<GiftInfo>) -> Unit,
        onError: (Throwable) -> Unit = {}
    ) {
        scope.launch(Dispatchers.IO) {
            try {
                val list = getGiftList(forceRefresh)
                ThreadUtils.runOnMain { onSuccess(list) }
            } catch (e: Exception) {
                ThreadUtils.runOnMain { onError(e) }
            }
        }
    }

    suspend fun loadAllGift() {
        if (giftCache.isNullOrEmpty()) {
            try {
                getGiftList(true)
            } catch (e: Exception) {
            }
        }
    }

    fun loadAllGift(
        scope: CoroutineScope,
        onSuccess: (List<GiftInfo>) -> Unit,
        onError: (Throwable) -> Unit = {}
    ) {
        getGiftList(scope, true, onSuccess, onError)
    }

    fun clearCache() {
        giftCache = null
    }
} 