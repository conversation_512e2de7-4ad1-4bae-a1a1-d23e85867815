package com.score.callmetest.manager

import com.score.callmetest.network.RearCameraConfigResponse
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object RearCameraManager {
    @Volatile
    var config: RearCameraConfigResponse? = null
        private set

    suspend fun fetchConfig(callback: (RearCameraConfigResponse?) -> Unit?) {
        var result: RearCameraConfigResponse? = null
        try {
            val resp = RetrofitUtils.apiService.getRearCameraConfig()
            if (resp.code == 0) {
                config = resp.data
                result = resp.data
            }
        } catch (_: Exception) {}
        ThreadUtils.runOnMain {
            callback(result)
        }
    }

    fun openRearCamera(scope: CoroutineScope, callback: (Boolean) -> Unit) {
        scope.launch(Dispatchers.IO) {
            var result = false
            try {
                val resp = RetrofitUtils.apiService.openRearCamera()
                if (resp.code == 0 && resp.data == true) {
                    fetchConfig { }
                    result = true
                }
            } catch (_: Exception) {}
            scope.launch(Dispatchers.Main) { callback(result) }
        }
    }

    /**
     * 清空摄像头配置
     */
    fun clear() {
        config = null
    }
} 