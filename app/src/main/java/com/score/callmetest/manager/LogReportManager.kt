package com.score.callmetest.manager

import android.os.Build
import com.score.callmetest.BuildConfig
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.network.LogReportData
import com.score.callmetest.network.LogReportRequest
import com.score.callmetest.network.PurchaseEventType
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.HeaderUtils
import com.score.callmetest.util.ThreadUtils
import timber.log.Timber
import java.util.UUID

/**
 * 日志上报管理器
 * 负责统一管理日志上报功能，包括通话流程行为打点和购买事件追踪
 *
 * 日志上报接口：https://test-log.callmeso.com/log/live-chat
 *
 * ## 使用说明
 *
 * ### 1. 通话流程行为打点
 *
 * 在通话相关的关键节点调用以下方法：
 *
 * ```kotlin
 * // 呼叫界面进入
 * LogReportManager.reportLiveCallEvent(
 *     action = LiveCallAction.ENTER,
 *     ext = LiveCallExt.CALLING
 * )
 *
 * // 通话界面进入
 * LogReportManager.reportLiveCallEvent(
 *     action = LiveCallAction.ENTER,
 *     ext = LiveCallExt.CHATTING
 * )
 *
 * // 点击挂断按钮
 * LogReportManager.reportLiveCallEvent(
 *     action = LiveCallAction.HANGUP,
 *     ext = LiveCallExt.CALLING,
 *     ext2 = LiveCallExt2.HANGUP_BUTTON
 * )
 *
 * // 接听通话
 * LogReportManager.reportLiveCallEvent(
 *     action = LiveCallAction.PICKUP,
 *     ext = LiveCallExt.CALLING
 * )
 *
 * // 拒绝通话
 * LogReportManager.reportLiveCallEvent(
 *     action = LiveCallAction.HANGUP,
 *     ext = LiveCallExt.CALLING,
 *     ext2 = LiveCallExt2.REJECT
 * )
 *
 * // 超时挂断
 * LogReportManager.reportLiveCallEvent(
 *     action = LiveCallAction.HANGUP,
 *     ext = LiveCallExt.CALLING,
 *     ext2 = LiveCallExt2.TIME_OUT
 * )
 *
 * // 通话挂断
 * LogReportManager.reportLiveCallEvent(
 *     action = LiveCallAction.HANGUP,
 *     ext = LiveCallExt.CHATTING,
 *     ext2 = "normal" // 或其他挂断原因
 * )
 *
 * // 退出界面
 * LogReportManager.reportLiveCallEvent(
 *     action = LiveCallAction.EXIT,
 *     ext = LiveCallExt.CALLING // 或 LiveCallExt.CHATTING
 * )
 * ```
 *
 * ### 2. 购买事件追踪
 *
 * 在购买流程的关键节点调用以下方法：
 *
 * ```kotlin
 * // 创建订单
 * LogReportManager.reportPurchaseEvent(
 *     event = PurchaseEventType.CREATE_ORDER,
 *     code = "product_id"
 * )
 *
 * // 创建订单响应
 * LogReportManager.reportPurchaseEvent(
 *     event = PurchaseEventType.CREATE_ORDER_RESPONSE,
 *     code = "product_id",
 *     orderId = "order_no",
 *     result = "success", // 或 "failed"
 *     resultCode = 0
 *     // durationTime 和 elapsedTime 会自动计算
 * )
 *
 * // 调起支付
 * LogReportManager.reportPurchaseEvent(
 *     event = PurchaseEventType.LAUNCH_PAY,
 *     code = "product_id"
 * )
 *
 * // 支付结果回调
 * LogReportManager.reportPurchaseEvent(
 *     event = PurchaseEventType.LAUNCH_PAY_RESPONSE,
 *     code = "product_id",
 *     orderId = "order_no",
 *     result = "success", // 或 "failed"
 *     resultCode = 0
 * )
 *
 * // 校验订单
 * LogReportManager.reportPurchaseEvent(
 *     event = PurchaseEventType.VERIFY_ORDER,
 *     orderId = "order_no"
 * )
 *
 * // 校验订单响应
 * LogReportManager.reportPurchaseEvent(
 *     event = PurchaseEventType.VERIFY_ORDER_RESPONSE,
 *     orderId = "order_no",
 *     result = "success", // 或 "failed"
 *     resultCode = 0
 * )
 *
 * // 消费订单
 * LogReportManager.reportPurchaseEvent(
 *     event = PurchaseEventType.CONSUME_ORDER,
 *     code = "product_id"
 * )
 *
 * // 消费订单响应
 * LogReportManager.reportPurchaseEvent(
 *     event = PurchaseEventType.CONSUME_ORDER_RESPONSE,
 *     result = "success", // 或 "failed"
 *     resultCode = 0
 * )
 * ```
 *
 * ### 3. 日志上报参数说明
 *
 * - `channelName`: 频道名称，用于把整个通话流程串起来
 * - `action`: 行为类型，如 enter、hangup、exit 等
 * - `ext`: 扩展信息，如 calling、chatting 等
 * - `ext2`: 扩展信息2，如 hangup_button、time_out 等
 * - `event`: 购买事件类型，如 create_order、launch_pay 等
 * - `code`: 商品ID
 * - `orderId`: 订单ID
 * - `durationTime`: 当前事务执行耗时（响应日志才有值）
 * - `elapsedTime`: 总耗时（从create_order开始到当前日志点的总耗时）
 * - `result`: 响应结果，success 或 failed
 * - `resultCode`: 响应代码
 *
 * ### 4. 自动上报的字段
 *
 * 以下字段会自动从系统获取并上报：
 * - `android_id`: Android设备ID
 * - `user_id`: 用户ID
 * - `pkg`: 包名
 * - `ver`: 版本号
 * - `platform`: 平台（Android）
 * - `platform_ver`: 系统版本
 * - `model`: 设备型号
 * - `sys_lan`: 系统语言
 * - `is_in_bg`: 是否在后台
 * - `is_anchor`: 是否为主播
 * - `tm`: 时间戳
 */
object LogReportManager {
    private const val TAG = "LogReport"

    // 日志上报计数器，用于sec_id字段
    private var sLogCounter = 0

    // 客户端启动唯一标识，用于区分是否是同一次启动
    private val sLaunchId = UUID.randomUUID().toString()

    // 购买事件UUID缓存，用于关联同一购买流程的日志
    private val purchaseUuidCache = mutableMapOf<String, String>()

    // 购买流程开始时间缓存，用于计算elapsedTime
    private val purchaseStartTimeCache = mutableMapOf<String, Long>()

    private val durationStartTimeCache = mutableMapOf<String, Long>()

    /**
     * 上报通话流程行为日志
     * @param channelName 频道名称，用于把整个流程串起来
     * @param action 行为类型
     * @param ext 扩展信息
     * @param ext2 扩展信息2
     */
    fun reportLiveCallEvent(
        channelName: String? = null,
        action: String,
        ext: String? = null,
        ext2: String? = null
    ) {
        val logData = LogReportData(
            channelName = channelName,
            action = action,
            ext = ext,
            ext2 = ext2,
            tm = System.currentTimeMillis()
        )

        reportLog(
            subtype = "livecall",
            logData = logData
        )

        Timber.tag(TAG)
            .d("上报通话流程日志: channelName=$channelName, action=$action, ext=$ext, ext2=$ext2")
    }

    /**
     * 上报购买事件追踪日志
     * @param event 事件类型
     * @param code 商品ID
     * @param orderId 订单ID
     * @param durationTime 当前事务执行耗时（响应日志才有值）
     * @param elapsedTime 总耗时（从create_order开始到当前日志点的总耗时）
     * @param result 响应结果
     * @param resultCode 响应code
     */
    fun reportPurchaseEvent(
        event: String,
        code: String? = null,
        orderId: String? = null,
        durationTime: Long? = null,
        elapsedTime: Long? = null,
        result: String? = Constant.SUCCESS,
        resultCode: Int? = 0
    ) {
        val productCode = code ?: ""
        val currentTime = System.currentTimeMillis()

        // 获取或生成购买流程UUID
        val uuid = getOrCreatePurchaseUuid(productCode)

        // 处理时间追踪
        var finalElapsedTime = elapsedTime
        var finalDurationTime = durationTime

        when (event) {
            PurchaseEventType.CREATE_ORDER -> {
                // 记录购买流程开始时间
                purchaseStartTimeCache[productCode] = currentTime
                durationStartTimeCache[productCode] = currentTime
                finalElapsedTime = 0L
                finalDurationTime = 0L
            }
            PurchaseEventType.REVIEW_ORDER,
            PurchaseEventType.LAUNCH_PAY,
            PurchaseEventType.VERIFY_ORDER,
            PurchaseEventType.CONSUME_ORDER,
            PurchaseEventType.ACKNOWLEDGED_ORDER-> {
                durationStartTimeCache[productCode] = currentTime
                finalDurationTime = 0L
            }
            else -> {
                val startTime = purchaseStartTimeCache[productCode]
                val durationStartTime = durationStartTimeCache[productCode]
                if (startTime != null) {
                    finalElapsedTime = currentTime - startTime
                }
                if (durationStartTime != null) {
                    finalDurationTime = currentTime - durationStartTime
                }
            }
        }

        val logData = LogReportData(
            event = event,
            code = code,
            uuid = uuid,
            orderId = orderId,
            durationTime = finalDurationTime,
            elapsedTime = finalElapsedTime,
            result = result,
            resultCode = resultCode,
            tm = currentTime
        )

        reportLog(
            subtype = "purchase_detail",
            logData = logData
        )

        Timber.tag(TAG)
            .d("上报购买事件日志: event=$event, code=$code, uuid=$uuid, orderId=$orderId, durationTime=$finalDurationTime, elapsedTime=$finalElapsedTime, result=$result, resultCode=$resultCode")
    }

    /**
     * 获取或创建购买流程UUID
     * @param code 商品ID
     * @return 购买流程UUID
     */
    private fun getOrCreatePurchaseUuid(code: String): String {
        return purchaseUuidCache.getOrPut(code) {
            UUID.randomUUID().toString()
        }
    }

    /**
     * 清理购买流程缓存
     * @param code 商品ID，如果为空则清理所有缓存
     */
    fun clearPurchaseCache(code: String? = null) {
        if (code == null) {
            purchaseUuidCache.clear()
            purchaseStartTimeCache.clear()
        } else {
            purchaseUuidCache.remove(code)
            purchaseStartTimeCache.remove(code)
        }
    }

    /**
     * 清理购买流程UUID缓存（保持向后兼容）
     * @param code 商品ID，如果为空则清理所有缓存
     */
    fun clearPurchaseUuid(code: String? = null) {
        clearPurchaseCache(code)
    }

    /**
     * 统一日志上报方法
     * @param subtype 日志类型
     * @param logData 日志数据
     */
    private fun reportLog(subtype: String, logData: LogReportData) {
        ThreadUtils.runOnIO {
            try {
                val context = CallmeApplication.context
                val userId = UserInfoManager.myUserInfo?.userId ?: ""

                // 构建日志上报请求
                val request = LogReportRequest(
                    list = listOf(logData),
                    subtype = subtype,
                    android_id = DeviceUtils.getAndroidId(),
                    utm_source = HeaderUtils.adjustAttribution?.let { attr ->
                        attr.network
                    } ?: "",
                    user_id = userId,
                    pkg = BuildConfig.APPLICATION_ID,
                    ver = BuildConfig.VERSION_NAME,
                    platform_ver = Build.VERSION.SDK_INT,
                    model = Build.MODEL,
                    lan_id = sLaunchId,
                    sec_id = getNextLogCounter(),
                    sys_lan = getSystemLanguage(),
                    country = getCurrentCountry(),
                    is_in_bg = AppLifecycleManager.isAppInBackground(),
                    is_anchor = isAnchor(),
                    tm = System.currentTimeMillis()
                )

                // 发送日志上报请求
                val response = RetrofitUtils.logApiService.reportLog(request)

                if (response.success) {
                    Timber.tag(TAG).d("日志上报成功: subtype=$subtype")
                } else {
                    Timber.tag(TAG)
                        .e("日志上报失败: subtype=$subtype, code=${response.code}, msg=${response.msg}")
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "日志上报异常: subtype=$subtype")
            }
        }
    }

    /**
     * 获取下一个日志计数器值
     */
    private fun getNextLogCounter(): Int {
        return ++sLogCounter
    }

    /**
     * 获取系统语言
     */
    private fun getSystemLanguage(): String {
        return try {
            val locale = CallmeApplication.context.resources.configuration.locales[0]
            locale.language
        } catch (e: Exception) {
            "en"
        }
    }

    /**
     * 获取当前国家
     */
    private fun getCurrentCountry(): String {
        return try {
            val locale = CallmeApplication.context.resources.configuration.locales[0]
            locale.country
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 判断是否为主播
     */
    private fun isAnchor(): Boolean {
        return UserInfoManager.myUserInfo?.userType == 2
    }

    /**
     * 重置日志计数器（应用重启时调用）
     */
    fun resetLogCounter() {
        sLogCounter = 0
    }
} 