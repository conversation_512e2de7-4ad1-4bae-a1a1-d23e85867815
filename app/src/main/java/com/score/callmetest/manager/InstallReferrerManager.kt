package com.score.callmetest.manager

import android.content.Context
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.score.callmetest.entity.InstallReferrerEntity
import com.score.callmetest.network.ApiService
import com.google.gson.Gson
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

object InstallReferrerManager {
    private const val PREFS_NAME = "install_referrer_prefs"
    private const val KEY_DATA = "install_referrer_data"
    private const val KEY_REPORTED = "install_referrer_reported"

    fun init(context: Context) {
        if (isReported(context)) {
            return
        }
        val referrerClient = InstallReferrerClient.newBuilder(context).build()
        referrerClient.startConnection(object : InstallReferrerStateListener {
            override fun onInstallReferrerSetupFinished(responseCode: Int) {
                when (responseCode) {
                    InstallReferrerClient.InstallReferrerResponse.OK -> {
                        try {
                            val referrerDetails = referrerClient.installReferrer
                            val entity = InstallReferrerEntity(
                                referrerUrl = referrerDetails.installReferrer ?: "",
                                installVersion = referrerDetails.installVersion ?: "unknown",
                                appInstallTime = referrerDetails.installBeginTimestampSeconds,
                                appInstallServerTime = referrerDetails.installBeginTimestampServerSeconds,
                                referrerClickTime = referrerDetails.referrerClickTimestampSeconds,
                                referrerClickServerTime = referrerDetails.referrerClickTimestampServerSeconds
                            )
                            save(context, entity)
                            Timber.tag("dsc--InstallReferrer").d("InstallReferrer saved: $entity")
                        } catch (e: Exception) {
                            Timber.tag("dsc--InstallReferrer").e(e, "Failed to get install referrer")
                        }
                    }
                    InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED -> {
                        Timber.tag("dsc--InstallReferrer").w("InstallReferrer API not supported")
                    }
                    InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE -> {
                        Timber.tag("dsc--InstallReferrer").w("InstallReferrer service unavailable")
                    }
                }
            }
            override fun onInstallReferrerServiceDisconnected() {
                Timber.tag("dsc--InstallReferrer").w("InstallReferrer service disconnected")
            }
        })
    }

    private fun save(context: Context, entity: InstallReferrerEntity) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit()
            .putString(KEY_DATA, Gson().toJson(entity))
            .putBoolean(KEY_REPORTED, false)
            .apply()
    }

    private fun get(context: Context): InstallReferrerEntity? {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val json = prefs.getString(KEY_DATA, null) ?: return null
        return Gson().fromJson(json, InstallReferrerEntity::class.java)
    }

    private fun isReported(context: Context): Boolean {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getBoolean(KEY_REPORTED, false)
    }

    private fun setReported(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putBoolean(KEY_REPORTED, true).apply()
    }

    fun tryReport(context: Context) {
        if (!isReported(context)) {
            val entity = get(context) ?: return
            ThreadUtils.runOnIO {
                try {
                    val response = RetrofitUtils.apiService.submitInstallReferrer(entity)
                    if (response.success == true && response.data == true) {
                        setReported(context)
                        Timber.tag("dsc--InstallReferrer").i("InstallReferrer reported successfully: $entity")
                    } else {
                        Timber.tag("dsc--InstallReferrer").w("InstallReferrer report failed: ${response.msg}, $entity")
                    }
                } catch (e: Exception) {
                    Timber.tag("dsc--InstallReferrer").e(e, "InstallReferrer report exception")
                }
            }
        }
    }

} 