package com.score.callmetest.entity

import android.os.Parcelable
import androidx.room.Ignore
import com.score.callmetest.CallStatus
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.UserInfo
import kotlinx.parcelize.Parcelize

/**
 * 消息列表数据模型
 * @property userId 用户ID
 * @property userName 用户名称
 * @property gender 性别
 * @property unitPrice 价格
 * @property avatar 头像URL
 * @property avatarThumbUrl 头像URL
 * @property lastMessage 最后一条消息内容
 * @property lastMessageType 最后一条消息内容类型
 * @property timestamp 时间戳显示文本
 * @property timeInMillis 时间戳毫秒值，用于排序
 * @property unreadCount 未读消息数
 * @property onlineStatus 在线状态
 * @property isPinned 是否置顶
 * @property isBottomView 是否是底部item
 */
@Parcelize
data class MessageListEntity(
    val userId: String,
    val userName: String,
    val gender: Int,
    val unitPrice: Int,
    var avatar: String,
    var avatarThumbUrl: String,
    var lastMessage: String,
    var lastMessageType: MessageType,
    var timestamp: String,
    var timeInMillis: Long = System.currentTimeMillis(),
    var unreadCount: Int = 0,
    var onlineStatus: String = CallStatus.OFFLINE,
    var isPinned: Boolean = false,
    var isBottomView: Boolean = false
) : Parcelable{

    companion object{
        fun provideBottomView(): MessageListEntity {
            return MessageListEntity(
                userId = "",
                userName = "",
                gender = 0,
                unitPrice = 0,
                avatar = "",
                avatarThumbUrl = "",
                lastMessage = "",
                lastMessageType = MessageType.TEXT,
                timestamp = "",
                timeInMillis = 0,
                unreadCount = 0,
                onlineStatus = CallStatus.OFFLINE,
                isPinned = false,
                isBottomView = true
            )
        }
    }

    fun toBroadcasterModel() : BroadcasterModel {
        return BroadcasterModel(
            userId = this.userId,
            avatarMapPath = this.avatar,
            avatarThumbUrl = this.avatarThumbUrl,
            nickname = this.userName,
            countdown = null, // 倒计时,  user里没有
            gender = this.gender,
            callCoins = this.unitPrice,
            videoMapPaths = null // 不需要
        )
    }

}