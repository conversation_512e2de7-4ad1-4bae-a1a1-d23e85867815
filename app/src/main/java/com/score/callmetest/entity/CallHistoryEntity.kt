package com.score.callmetest.entity

import android.os.Parcelable
import com.score.callmetest.CallStatus
import com.score.callmetest.CallType
import com.score.callmetest.network.BroadcasterModel
import kotlinx.parcelize.Parcelize


/**
 * 通话历史数据模型
 * @property id 通话历史ID
 * @property userId 用户ID
 * @property userName 用户名称
 * @property avatar 头像URL
 * @property unitPrice 价格
 * @property callType 通话类型
 * @property callDuration 通话时长（秒）
 * @property callEndTime 通话结束时间
 * @property hasVideo 是否为视频通话
 * @property onlineStatus 在线状态
 * @property isBottomView 是否是底部item
 * @property isPinned 是否置顶
 */
@Parcelize
data class CallHistoryEntity(
    val id: String,
    val userId: String,
    val userName: String,
    val avatar: String,
    val unitPrice: Int,
    val callType: String = CallType.UNANSWERED_CALL,
    val callDuration: Long = 0, // 通话时长（秒）
    val callEndTime: Long = System.currentTimeMillis(), // 通话结束时间
    val hasVideo: Boolean = true, // 是否为视频通话
    val onlineStatus: String = CallStatus.OFFLINE, // 是否在线
    var isPinned: Boolean = false,
    var isBottomView: Boolean = true
) : Parcelable {
    companion object{
        fun provideBottomView(): CallHistoryEntity {
            return CallHistoryEntity(
                id = "",
                userId = "",
                userName = "",
                avatar = "",
                unitPrice = 0,
                callType = CallType.UNANSWERED_CALL,
                callDuration = 0,
                callEndTime = 0,
                hasVideo = false,
                onlineStatus = CallStatus.OFFLINE,
                isPinned = false,
                isBottomView = true
            )
        }
    }
    fun toBroadcasterModel() : BroadcasterModel {
        return BroadcasterModel(
            userId = this.userId,
            avatarMapPath = this.avatar,
            avatarThumbUrl = "",
            nickname = this.userName,
            countdown = null,
            gender = null,
            callCoins = this.unitPrice,
            videoMapPaths = null
        )
    }

    
    /**
     * 获取通话时长显示文本
     * @return 通话时长显示文本，格式：mm:ss 或 hh:mm:ss
     */
    fun getCallDurationText(): String {
        // 只有成功进入通话才有通话时间记录
        if (callType != CallType.OUTGOING_CALL && callType != CallType.INCOMING_CALL) {
            return ""
        }

        val hours = callDuration / 3600
        val minutes = (callDuration % 3600) / 60
        val seconds = callDuration % 60

        return when {
            hours > 0 -> String.format("%d:%02d:%02d", hours, minutes, seconds)
            else -> String.format("%02d:%02d", minutes, seconds)
        }
    }
} 