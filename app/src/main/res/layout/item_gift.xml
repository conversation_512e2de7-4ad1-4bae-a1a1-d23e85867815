<?xml version="1.0" encoding="utf-8"?>
<com.score.callmetest.ui.widget.AlphaRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/gift_item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_gift_item_selector"
    android:padding="8dp">

    <ImageView
        android:id="@+id/iv_gift"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_centerHorizontal="true"
        android:scaleType="centerCrop"
        android:src="@drawable/gift_placehold" />

    <LinearLayout
        android:id="@+id/coin_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_gift"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="12dp"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:src="@drawable/coin" />

        <TextView
            android:id="@+id/tv_gift_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="2dp"
            android:text="100"
            android:textColor="@color/white"
            android:textSize="12sp" />
    </LinearLayout>

    <com.score.callmetest.ui.widget.AlphaTextView
        android:id="@+id/send"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_gift"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:padding="5dp"
        android:layout_marginTop="14dp"
        android:text="Send"
        android:textColor="@color/black"
        android:textSize="14sp"
        android:visibility="gone" />
</com.score.callmetest.ui.widget.AlphaRelativeLayout>