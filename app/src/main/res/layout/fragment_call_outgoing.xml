<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <!-- 头像全屏展示 -->
    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="avatar"
        android:scaleType="centerCrop" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/photo_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 底部渐变遮罩 -->
    <View
        android:id="@+id/bottom_shadow"
        android:layout_width="match_parent"
        android:layout_height="460dp"
        android:layout_gravity="bottom"
        android:orientation="vertical" />

    <!-- 底部信息和操作区 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="260dp"
        android:layout_gravity="bottom"
        android:layout_marginBottom="32dp"
        android:background="@android:color/transparent"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_nickname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="CCHUBBY"
            android:textColor="#FFFFFF"
            android:textSize="28sp"
            android:textStyle="bold" />


        <!-- 标签和国家 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/age_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="6dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp">

                <ImageView
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:src="@drawable/chat_girl" />

                <TextView
                    android:id="@+id/tv_age"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="22"
                    android:textColor="@color/age_color"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:typeface="sans" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_country"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                android:text="India"
                android:textColor="@color/country_color"
                android:textSize="12sp"
                android:textStyle="bold"
                android:typeface="sans" />
        </LinearLayout>

        <!-- 价格说明 -->
        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="40dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="24dp"
            android:gravity="center"
            android:text="You will cost 120🍋/min"
            android:textColor="#FFFFFF"
            android:textSize="20sp" />

        <!-- 挂断按钮 -->
        <ImageButton
            android:id="@+id/btn_cancel"
            android:layout_width="74dp"
            android:layout_height="74dp"
            android:layout_gravity="center_horizontal"
            android:background="@android:color/transparent"
            android:contentDescription="hang up"
            android:scaleType="centerInside"
            android:src="@drawable/ic_hangup" />
    </LinearLayout>
</FrameLayout> 