<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_gift_bottomsheet">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_gift"
        android:layout_width="match_parent"
        android:layout_height="314dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="20dp"
        android:clipToPadding="false"
        android:overScrollMode="never"
        android:paddingTop="16dp"
        android:paddingBottom="75dp" />

    <!--    遮罩层-->
    <View
        android:id="@+id/mask_view"
        android:layout_width="match_parent"
        android:layout_height="115dp"
        android:layout_gravity="bottom" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <com.score.callmetest.ui.widget.AlphaLinearLayout
            android:id="@+id/coin_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/coin" />

            <TextView
                android:id="@+id/coin_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1000"
                android:textColor="@color/white"
                android:layout_marginStart="4dp"
                android:textSize="16sp"
                android:textStyle="bold" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:tintMode="src_in"
                android:layout_marginStart="2dp"
                android:src="@drawable/ic_chevron_right"
                app:tint="@color/white" />
        </com.score.callmetest.ui.widget.AlphaLinearLayout>


        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/btn_recharge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="Recharge"
            android:paddingVertical="9dp"
            android:paddingHorizontal="14dp"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:textStyle="bold" />
    </RelativeLayout>

</FrameLayout>