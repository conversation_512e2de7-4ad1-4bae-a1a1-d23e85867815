<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@android:color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- 背景动画容器，仿照ChatActivity的gift_animation_bg_image -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/bg_animation_image"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:layout_marginTop="8dp"
            android:scaleType="fitCenter"
            android:src="@drawable/gift_light_bg"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="62dp"
            android:layout_height="62dp"
            android:layout_marginTop="12dp"
            android:src="@drawable/coin"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- HOT标签 - 确保在iv_icon上面 -->
        <TextView
            android:id="@+id/tv_hot_red"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:background="@drawable/bg_item_recharge_hot"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="HOT"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            android:visibility="visible"
            android:elevation="2dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/line_coin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_icon">

            <TextView
                android:id="@+id/tv_coin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="100"
                android:textAlignment="center"
                android:textColor="@color/black"
                android:textSize="17sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/image_coin"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginStart="2dp"
                android:layout_gravity="center"
                android:src="@drawable/coin" />

            <TextView
                android:id="@+id/tv_bonus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:layout_gravity="center"
                android:text="+50%"
                android:textAlignment="center"
                android:textColor="@color/red_coin_store"
                android:textSize="14sp" />

        </LinearLayout>


        <com.score.callmetest.ui.widget.AlphaLinearLayout
            android:id="@+id/price_layout"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="15dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="18dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/line_coin">

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="$9.99"
                android:textColor="#000"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_old_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:paddingStart="2dp"
                android:text="$19.99"
                android:textColor="#888"
                android:textSize="12sp" />
        </com.score.callmetest.ui.widget.AlphaLinearLayout>

        <LinearLayout
            android:id="@+id/unselected_layout"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:gravity="center"

            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/unselcted_tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="$9.99"
                android:textColor="#000"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/unselcted_tv_old_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:paddingStart="2dp"
                android:text="$19.99"
                android:textColor="#888"
                android:textSize="12sp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
