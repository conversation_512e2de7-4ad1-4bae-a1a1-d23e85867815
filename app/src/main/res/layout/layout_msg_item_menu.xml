<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardCornerRadius="12dp"
    app:cardElevation="5dp"
    app:cardMaxElevation="5dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">


        <!--   hidden     -->
        <LinearLayout
            android:id="@+id/layout_msg_item_menu_hide"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/bg_mine_function_item_selector"
            android:gravity="center_vertical|start"
            android:orientation="horizontal"
            android:paddingStart="21dp"
            android:paddingEnd="30dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="fitCenter"
                android:src="@drawable/hidden" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="0dp"
                android:text="@string/msg_item_menu_hide"
                android:textAllCaps="false"
                android:textColor="@color/black"
                android:textSize="14sp" />

        </LinearLayout>

        <!--   delete     -->
        <LinearLayout
            android:id="@+id/layout_msg_item_menu_delete"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/bg_mine_function_item_selector"
            android:gravity="center_vertical|start"
            android:orientation="horizontal"
            android:paddingStart="21dp"
            android:paddingEnd="30dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="fitCenter"
                android:src="@drawable/delete" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="0dp"
                android:text="@string/msg_item_menu_delete"
                android:textAllCaps="false"
                android:textColor="@color/black"
                android:textSize="14sp" />

        </LinearLayout>

        <!--   pin     -->
        <LinearLayout
            android:id="@+id/layout_msg_item_menu_pin"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/bg_mine_function_item_selector"
            android:gravity="center_vertical|start"
            android:orientation="horizontal"
            android:paddingStart="21dp"
            android:paddingEnd="30dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="fitCenter"
                android:src="@drawable/pin" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_msg_item_menu_pin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="0dp"
                android:text="@string/msg_item_menu_pin"
                android:textAllCaps="false"
                android:textColor="@color/black"
                android:textSize="14sp" />

        </LinearLayout>


    </LinearLayout>

</androidx.cardview.widget.CardView>