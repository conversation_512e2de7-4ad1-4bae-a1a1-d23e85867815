<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:elevation="5dp"
    android:background="@color/recording_bg">

    <!-- 正常录音状态 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/recording_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible">

        <View
            android:id="@+id/recording_bottom_view"
            android:layout_width="match_parent"
            android:layout_height="169dp"
            android:background="@drawable/record_ing"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!--   录音计时     -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvRecordTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="35dp"
            android:text="@string/recording_timer"
            android:textColor="@color/white"
            android:textSize="15sp"
            app:layout_constraintBottom_toTopOf="@id/recording_bottom_view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!--   录音动画     -->
        <LinearLayout
            android:layout_width="145dp"
            android:layout_height="84dp"
            android:layout_marginBottom="55dp"
            android:background="@drawable/record_top_bg"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintBottom_toTopOf="@id/tvRecordTime"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/recording_img"
                android:layout_width="106dp"
                android:layout_height="37dp"
                android:layout_marginTop="10dp"
                android:scaleType="fitCenter"
                android:src="@drawable/record_animation" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/recording_tv_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:text="@string/recording_hint"
                android:textColor="@color/white"
                android:textSize="11sp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 取消录音状态 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/record_hold_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="gone">

        <View
            android:id="@+id/record_hold_bottom_view"
            android:layout_width="match_parent"
            android:layout_height="169dp"
            android:background="@drawable/record_hold"
            app:layout_constraintBottom_toBottomOf="parent" />

        <LinearLayout
            android:layout_width="144dp"
            android:layout_height="64dp"
            android:layout_marginBottom="94dp"
            android:background="@drawable/record_hold_top_bg"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintBottom_toTopOf="@id/record_hold_bottom_view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/record_hold_tv_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:text="@string/recording_hint"
                android:textColor="@color/white"
                android:textSize="15sp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 滑动取消指示线 -->
    <View
        android:id="@+id/viewCancelLine"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="170dp"
        android:background="@color/transparent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 