<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg"
    android:orientation="vertical">

    <!--    <androidx.appcompat.widget.AppCompatImageView-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="88dp"-->
    <!--        android:background="@mipmap/main_bg" />-->
    <!--顶部导航栏-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="44dp"
        android:orientation="vertical">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="0dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="44dp">

                <com.score.callmetest.ui.widget.AlphaImageView
                    android:id="@+id/iv_back"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_centerVertical="true"
                    android:src="@drawable/nav_btn_back" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="Settings"
                    android:textColor="@android:color/black"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/about_us_bg">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:padding="0dp">

                <!-- Switch 1: Auto Translate -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Auto Translate"
                        android:textColor="@color/black"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <Switch
                        android:id="@+id/switchAutoTranslate"
                        style="@style/NoRippleSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="5dp"
                        android:thumb="@drawable/switch_thumb"
                        android:track="@drawable/switch_track" />
                </LinearLayout>
                <!-- Switch 2: Do not disturb - Call -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Do not disturb - Call"
                        android:textColor="@color/black"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <Switch
                        android:id="@+id/switchDndCall"
                        style="@style/NoRippleSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="5dp"
                        android:thumb="@drawable/switch_thumb"
                        android:track="@drawable/switch_track" />
                </LinearLayout>
                <!-- Switch 3: Do not disturb - Message -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Do not disturb - Message"
                        android:textColor="@color/black"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <Switch
                        android:id="@+id/switchDndMsg"
                        style="@style/NoRippleSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="5dp"
                        android:thumb="@drawable/switch_thumb"
                        android:track="@drawable/switch_track" />
                </LinearLayout>

                <!-- 分割线 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:background="@color/divide_line" />

                <!-- Blocklist -->
                <LinearLayout
                    android:id="@+id/itemBlocklist"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Blocklist"
                        android:textColor="@color/black"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right" />
                </LinearLayout>
                <!-- About Us -->
                <LinearLayout
                    android:id="@+id/itemAboutUs"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="About Us"
                        android:textColor="@color/black"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right" />
                </LinearLayout>
                <!-- Version -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Version"
                        android:textColor="@color/black"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <TextView
                        android:id="@+id/tvVersion"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4.2.1"
                        android:textColor="#888"
                        android:textSize="14sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right" />
                </LinearLayout>
                <!-- delete account -->
                <LinearLayout
                    android:id="@+id/item_delete_account"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Delete Account"
                        android:textColor="@color/black"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right" />
                </LinearLayout>
                <!-- 分割线 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:background="@color/divide_line" />

                <!-- Google -->
                <LinearLayout
                    android:id="@+id/itemGoogle"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Google"
                        android:textColor="@color/black"
                        android:textSize="15sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right" />
                </LinearLayout>

                <!-- Gmail -->
                <LinearLayout
                    android:id="@+id/itemGmail"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Gmail"
                        android:textColor="@color/black"
                        android:textSize="15sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right" />
                </LinearLayout>

                <!-- 分割线 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:background="@color/divide_line" />

                <!-- Log out -->
                <LinearLayout
                    android:id="@+id/itemLogout"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Log out"
                        android:textColor="@color/black"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right" />
                </LinearLayout>
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</FrameLayout>